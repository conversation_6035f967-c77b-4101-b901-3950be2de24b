from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.utils import timezone
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
import boto3
import json
from datetime import datetime
import pandas as pd
from io import BytesIO

from .models import AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog
from .serializers import (
    AWSAccountSerializer, EC2InstanceSerializer, EC2InstanceListSerializer,
    SSMStatusSerializer, InventoryRefreshLogSerializer, InventoryStatsSerializer
)
from .aws_services import EC2InventoryService, SSMStatusService


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


class AWSAccountViewSet(viewsets.ModelViewSet):
    queryset = AWSAccount.objects.all()
    serializer_class = AWSAccountSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        queryset = AWSAccount.objects.all()
        business_unit = self.request.query_params.get('business_unit', None)
        if business_unit:
            queryset = queryset.filter(business_unit__icontains=business_unit)
        return queryset

    @action(detail=True, methods=['post'])
    def refresh_inventory(self, request, pk=None):
        """Trigger inventory refresh for a specific account"""
        account = self.get_object()
        try:
            # Create refresh log
            refresh_log = InventoryRefreshLog.objects.create(
                account=account,
                status='running'
            )

            # Initialize AWS service
            ec2_service = EC2InventoryService(account)
            ssm_service = SSMStatusService(account)

            # Refresh EC2 instances
            instances_data = ec2_service.get_instances()
            instances_processed = 0
            errors_count = 0

            for instance_data in instances_data:
                try:
                    # Update or create EC2 instance
                    instance, created = EC2Instance.objects.update_or_create(
                        account=account,
                        instance_id=instance_data['instance_id'],
                        defaults=instance_data
                    )

                    # Update SSM status
                    ssm_data = ssm_service.get_instance_ssm_status(instance_data['instance_id'])
                    if ssm_data:
                        SSMStatus.objects.update_or_create(
                            instance=instance,
                            defaults=ssm_data
                        )

                    instances_processed += 1
                except Exception as e:
                    errors_count += 1
                    print(f"Error processing instance {instance_data.get('instance_id', 'unknown')}: {str(e)}")

            # Update refresh log
            refresh_log.status = 'completed'
            refresh_log.completed_at = timezone.now()
            refresh_log.instances_processed = instances_processed
            refresh_log.errors_count = errors_count
            refresh_log.save()

            return Response({
                'status': 'success',
                'message': f'Inventory refreshed successfully. Processed {instances_processed} instances.',
                'instances_processed': instances_processed,
                'errors_count': errors_count
            })

        except Exception as e:
            if 'refresh_log' in locals():
                refresh_log.status = 'failed'
                refresh_log.completed_at = timezone.now()
                refresh_log.error_details = str(e)
                refresh_log.save()

            return Response({
                'status': 'error',
                'message': f'Failed to refresh inventory: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EC2InstanceViewSet(viewsets.ModelViewSet):
    queryset = EC2Instance.objects.select_related('account', 'ssm_status').all()
    pagination_class = StandardResultsSetPagination

    def get_serializer_class(self):
        if self.action == 'list':
            return EC2InstanceListSerializer
        return EC2InstanceSerializer

    def get_queryset(self):
        queryset = EC2Instance.objects.select_related('account', 'ssm_status').all()

        # Filter parameters
        account_id = self.request.query_params.get('account_id', None)
        business_unit = self.request.query_params.get('business_unit', None)
        state = self.request.query_params.get('state', None)
        instance_type = self.request.query_params.get('instance_type', None)
        env_tag = self.request.query_params.get('env_tag', None)
        search = self.request.query_params.get('search', None)

        if account_id:
            queryset = queryset.filter(account__account_id=account_id)
        if business_unit:
            queryset = queryset.filter(account__business_unit__icontains=business_unit)
        if state:
            queryset = queryset.filter(state=state)
        if instance_type:
            queryset = queryset.filter(instance_type__icontains=instance_type)
        if env_tag:
            queryset = queryset.filter(env_tag__icontains=env_tag)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(instance_id__icontains=search) |
                Q(private_ip_address__icontains=search)
            )

        return queryset

    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        """Export EC2 instances to Excel"""
        queryset = self.get_queryset()

        # Prepare data for Excel
        data = []
        for instance in queryset:
            ssm_status = getattr(instance, 'ssm_status', None)
            data.append({
                'Business Unit': instance.account.business_unit,
                'Account Name': instance.account.account_name,
                'Account ID': instance.account.account_id,
                'Region': instance.account.region,
                'Instance ID': instance.instance_id,
                'Name': instance.name,
                'State': instance.state,
                'Instance Type': instance.instance_type,
                'vCPU': instance.vcpu_count,
                'Memory (GB)': instance.memory_gb,
                'Private IP': instance.private_ip_address,
                'OS Information': instance.os_information,
                'Environment': instance.env_tag,
                'UAI': instance.uai_tag,
                'Patch': instance.patch_tag,
                'CTO Cloud Ops Managed': instance.cto_cloud_ops_managed,
                'SSM Status': ssm_status.ping_status if ssm_status else 'N/A',
                'SSM Agent Version': ssm_status.agent_version if ssm_status else 'N/A',
                'Last Updated': instance.last_updated.strftime('%Y-%m-%d %H:%M:%S'),
            })

        # Create Excel file
        df = pd.DataFrame(data)
        output = BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='EC2_Inventory')

        output.seek(0)

        # Create response
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="ec2_inventory_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response


class SSMStatusViewSet(viewsets.ModelViewSet):
    queryset = SSMStatus.objects.select_related('instance', 'instance__account').all()
    serializer_class = SSMStatusSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        queryset = SSMStatus.objects.select_related('instance', 'instance__account').all()

        ping_status = self.request.query_params.get('ping_status', None)
        account_id = self.request.query_params.get('account_id', None)

        if ping_status:
            queryset = queryset.filter(ping_status=ping_status)
        if account_id:
            queryset = queryset.filter(instance__account__account_id=account_id)

        return queryset


@api_view(['GET'])
def dashboard_stats(request):
    """Get dashboard statistics"""
    try:
        total_accounts = AWSAccount.objects.filter(is_active=True).count()
        total_instances = EC2Instance.objects.count()
        running_instances = EC2Instance.objects.filter(state='running').count()
        stopped_instances = EC2Instance.objects.filter(state='stopped').count()

        ssm_online = SSMStatus.objects.filter(ping_status='Online').count()
        ssm_offline = SSMStatus.objects.exclude(ping_status='Online').count()

        # Get last refresh time
        last_refresh_log = InventoryRefreshLog.objects.filter(
            status='completed'
        ).order_by('-completed_at').first()

        last_refresh = last_refresh_log.completed_at if last_refresh_log else None

        stats = {
            'total_accounts': total_accounts,
            'total_instances': total_instances,
            'running_instances': running_instances,
            'stopped_instances': stopped_instances,
            'ssm_online': ssm_online,
            'ssm_offline': ssm_offline,
            'last_refresh': last_refresh
        }

        serializer = InventoryStatsSerializer(stats)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def refresh_all_inventory(request):
    """Refresh inventory for all active accounts"""
    try:
        active_accounts = AWSAccount.objects.filter(is_active=True)
        total_processed = 0
        total_errors = 0

        for account in active_accounts:
            try:
                # Create refresh log
                refresh_log = InventoryRefreshLog.objects.create(
                    account=account,
                    status='running'
                )

                # Initialize AWS services
                ec2_service = EC2InventoryService(account)
                ssm_service = SSMStatusService(account)

                # Refresh EC2 instances
                instances_data = ec2_service.get_instances()
                instances_processed = 0
                errors_count = 0

                for instance_data in instances_data:
                    try:
                        # Update or create EC2 instance
                        instance, created = EC2Instance.objects.update_or_create(
                            account=account,
                            instance_id=instance_data['instance_id'],
                            defaults=instance_data
                        )

                        # Update SSM status
                        ssm_data = ssm_service.get_instance_ssm_status(instance_data['instance_id'])
                        if ssm_data:
                            SSMStatus.objects.update_or_create(
                                instance=instance,
                                defaults=ssm_data
                            )

                        instances_processed += 1
                    except Exception as e:
                        errors_count += 1
                        print(f"Error processing instance {instance_data.get('instance_id', 'unknown')}: {str(e)}")

                # Update refresh log
                refresh_log.status = 'completed'
                refresh_log.completed_at = timezone.now()
                refresh_log.instances_processed = instances_processed
                refresh_log.errors_count = errors_count
                refresh_log.save()

                total_processed += instances_processed
                total_errors += errors_count

            except Exception as e:
                if 'refresh_log' in locals():
                    refresh_log.status = 'failed'
                    refresh_log.completed_at = timezone.now()
                    refresh_log.error_details = str(e)
                    refresh_log.save()

                total_errors += 1
                print(f"Error processing account {account.account_id}: {str(e)}")

        return Response({
            'status': 'success',
            'message': f'Inventory refresh completed. Processed {total_processed} instances across {active_accounts.count()} accounts.',
            'total_processed': total_processed,
            'total_errors': total_errors
        })

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'Failed to refresh inventory: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Template views for the frontend
def dashboard_view(request):
    """Dashboard page"""
    return render(request, 'inventory/dashboard.html')


def ec2_instances_view(request):
    """EC2 instances page"""
    return render(request, 'inventory/ec2_instances.html')


def ssm_status_view(request):
    """SSM status page"""
    return render(request, 'inventory/ssm_status.html')


def accounts_view(request):
    """Accounts management page"""
    return render(request, 'inventory/accounts.html')
