from django.utils import timezone
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from .models import UserSession, UserActivity
from .signals import get_client_ip


class UserSessionMiddleware:
    """
    Middleware to track user sessions and handle security
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Update session activity for authenticated users
        if request.user.is_authenticated and hasattr(request, 'session'):
            self.update_session_activity(request)
        
        response = self.get_response(request)
        return response

    def update_session_activity(self, request):
        """Update last activity for current session"""
        session_key = request.session.session_key
        if session_key:
            try:
                user_session = UserSession.objects.get(
                    session_key=session_key,
                    user=request.user,
                    is_active=True
                )
                user_session.last_activity = timezone.now()
                user_session.save(update_fields=['last_activity'])
            except UserSession.DoesNotExist:
                # Create session record if it doesn't exist
                ip_address = get_client_ip(request)
                user_agent = request.META.get('HTTP_USER_AGENT', '')
                
                UserSession.objects.create(
                    user=request.user,
                    session_key=session_key,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    is_active=True
                )


class AccountSecurityMiddleware:
    """
    Middleware to enforce account security policies
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check account security for authenticated users
        if request.user.is_authenticated:
            # Skip security checks for certain URLs
            if self.should_skip_security_check(request):
                return self.get_response(request)
            
            # Check if account is locked
            if request.user.is_account_locked():
                logout(request)
                messages.error(
                    request,
                    'Your account has been temporarily locked due to security reasons. '
                    'Please try again later or contact an administrator.'
                )
                return redirect('accounts:login')
            
            # Check if account is approved
            if not request.user.is_approved and not request.user.is_superuser:
                # Allow access to profile and logout pages
                allowed_paths = [
                    reverse('accounts:profile'),
                    reverse('accounts:logout'),
                    reverse('accounts:change_password'),
                ]
                
                if request.path not in allowed_paths:
                    messages.warning(
                        request,
                        'Your account is pending approval. You have limited access until approved.'
                    )
                    return redirect('accounts:profile')
        
        response = self.get_response(request)
        return response

    def should_skip_security_check(self, request):
        """Check if security checks should be skipped for this request"""
        skip_paths = [
            reverse('accounts:logout'),
            '/admin/',  # Skip for admin interface
        ]
        
        return any(request.path.startswith(path) for path in skip_paths)


class ActivityTrackingMiddleware:
    """
    Middleware to track user activities
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Track certain activities for authenticated users
        if request.user.is_authenticated and request.method in ['POST', 'PUT', 'DELETE']:
            self.track_activity(request, response)
        
        return response

    def track_activity(self, request, response):
        """Track user activities based on request"""
        # Only track successful requests
        if response.status_code >= 400:
            return
        
        path = request.path
        method = request.method
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Determine activity type based on path and method
        activity_data = self.get_activity_data(path, method)
        
        if activity_data:
            UserActivity.objects.create(
                user=request.user,
                action=activity_data['action'],
                description=activity_data['description'],
                ip_address=ip_address,
                user_agent=user_agent,
                additional_data={
                    'path': path,
                    'method': method,
                    'status_code': response.status_code
                }
            )

    def get_activity_data(self, path, method):
        """Determine activity type based on path and method"""
        activity_patterns = [
            # Inventory activities
            {
                'pattern': '/api/ec2-instances/',
                'methods': ['GET'],
                'action': 'view_inventory',
                'description': 'Viewed EC2 instances'
            },
            {
                'pattern': '/api/eks-clusters/',
                'methods': ['GET'],
                'action': 'view_inventory',
                'description': 'Viewed EKS clusters'
            },
            {
                'pattern': '/api/accounts/',
                'methods': ['GET'],
                'action': 'view_inventory',
                'description': 'Viewed AWS accounts'
            },
            # Export activities
            {
                'pattern': '/export_excel/',
                'methods': ['GET'],
                'action': 'export_data',
                'description': 'Exported data to Excel'
            },
            # User management activities
            {
                'pattern': '/accounts/users/',
                'methods': ['POST', 'PUT'],
                'action': 'manage_users',
                'description': 'Modified user account'
            },
            # System configuration
            {
                'pattern': '/admin/',
                'methods': ['POST', 'PUT', 'DELETE'],
                'action': 'system_config',
                'description': 'Modified system configuration'
            },
        ]
        
        for pattern in activity_patterns:
            if pattern['pattern'] in path and method in pattern['methods']:
                return {
                    'action': pattern['action'],
                    'description': pattern['description']
                }
        
        return None


class ConcurrentSessionMiddleware:
    """
    Middleware to handle concurrent session limits
    """
    
    MAX_CONCURRENT_SESSIONS = 3  # Maximum concurrent sessions per user
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.user.is_authenticated:
            self.check_concurrent_sessions(request)
        
        response = self.get_response(request)
        return response

    def check_concurrent_sessions(self, request):
        """Check and enforce concurrent session limits"""
        current_session_key = request.session.session_key
        
        # Get all active sessions for this user
        active_sessions = UserSession.objects.filter(
            user=request.user,
            is_active=True
        ).exclude(session_key=current_session_key)
        
        # If user has too many sessions, deactivate oldest ones
        if active_sessions.count() >= self.MAX_CONCURRENT_SESSIONS:
            # Keep only the most recent sessions
            sessions_to_keep = active_sessions.order_by('-last_activity')[
                :self.MAX_CONCURRENT_SESSIONS - 1
            ]
            
            # Deactivate older sessions
            sessions_to_deactivate = active_sessions.exclude(
                id__in=[s.id for s in sessions_to_keep]
            )
            
            for session in sessions_to_deactivate:
                session.end_session()
                
                # Log the session termination
                UserActivity.objects.create(
                    user=request.user,
                    action='logout',
                    description='Session terminated due to concurrent session limit',
                    ip_address=session.ip_address,
                    additional_data={
                        'reason': 'concurrent_session_limit',
                        'terminated_session': session.session_key
                    }
                )


class SAMLMiddleware:
    """
    Middleware for SAML SSO integration (future implementation)
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Future SAML SSO logic will go here
        # This middleware is prepared for future SAML integration
        
        response = self.get_response(request)
        return response

    def process_saml_response(self, request):
        """Process SAML response (future implementation)"""
        # This method will handle SAML response processing
        # when SAML SSO is implemented
        pass

    def initiate_saml_login(self, request):
        """Initiate SAML login (future implementation)"""
        # This method will handle SAML login initiation
        # when SAML SSO is implemented
        pass
