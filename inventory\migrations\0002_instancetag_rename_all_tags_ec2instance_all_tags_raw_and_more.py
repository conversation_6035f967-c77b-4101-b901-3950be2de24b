# Generated by Django 4.2.7 on 2025-06-11 14:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='InstanceTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_index=True, max_length=255)),
                ('value', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['key', 'value'],
            },
        ),
        migrations.RenameField(
            model_name='ec2instance',
            old_name='all_tags',
            new_name='all_tags_raw',
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='ami_id',
            field=models.Char<PERSON>ield(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='instance_profile',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='security_groups',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='subnet_id',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='subnet_name',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='cto_cloud_ops_managed',
            field=models.CharField(blank=True, db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='env_tag',
            field=models.CharField(blank=True, db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='instance_id',
            field=models.CharField(db_index=True, max_length=20),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='instance_type',
            field=models.CharField(db_index=True, max_length=50),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='name',
            field=models.CharField(blank=True, db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='patch_exempt',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='patch_group',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='patch_provider',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='private_ip_address',
            field=models.GenericIPAddressField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='state',
            field=models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('shutting-down', 'Shutting Down'), ('terminated', 'Terminated'), ('stopping', 'Stopping'), ('stopped', 'Stopped')], db_index=True, max_length=20),
        ),
        migrations.AlterField(
            model_name='ec2instance',
            name='uai_tag',
            field=models.CharField(blank=True, db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='ssmstatus',
            name='ping_status',
            field=models.CharField(blank=True, choices=[('Online', 'Online'), ('Connection Lost', 'Connection Lost'), ('Inactive', 'Inactive'), ('Unknown', 'Unknown')], db_index=True, max_length=20),
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['account', 'instance_id'], name='inventory_e_account_349302_idx'),
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['state', 'instance_type'], name='inventory_e_state_9df6a6_idx'),
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['env_tag', 'uai_tag'], name='inventory_e_env_tag_7d4224_idx'),
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['cto_cloud_ops_managed'], name='inventory_e_cto_clo_ba8a45_idx'),
        ),
        migrations.AddIndex(
            model_name='ssmstatus',
            index=models.Index(fields=['ping_status'], name='inventory_s_ping_st_b99f72_idx'),
        ),
        migrations.AddIndex(
            model_name='ssmstatus',
            index=models.Index(fields=['is_latest_version'], name='inventory_s_is_late_8386a0_idx'),
        ),
        migrations.AddIndex(
            model_name='instancetag',
            index=models.Index(fields=['key'], name='inventory_i_key_1cbd62_idx'),
        ),
        migrations.AddIndex(
            model_name='instancetag',
            index=models.Index(fields=['key', 'value'], name='inventory_i_key_2cf2cd_idx'),
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='instances', to='inventory.instancetag'),
        ),
    ]
