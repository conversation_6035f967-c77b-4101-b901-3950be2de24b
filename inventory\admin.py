from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.utils.safestring import mark_safe
from django.core.exceptions import ValidationError
import csv
import io
from .models import AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog, InstanceTag


@admin.register(InstanceTag)
class InstanceTagAdmin(admin.ModelAdmin):
    list_display = ['key', 'value', 'instances_count', 'created_at']
    list_filter = ['key']
    search_fields = ['key', 'value']
    ordering = ['key', 'value']

    def instances_count(self, obj):
        return obj.instances.count()
    instances_count.short_description = 'Instances Count'


@admin.register(AWSAccount)
class AWSAccountAdmin(admin.ModelAdmin):
    list_display = ['account_name', 'account_id', 'business_unit', 'region', 'is_active', 'created_at']
    list_filter = ['business_unit', 'region', 'is_active']
    search_fields = ['account_name', 'account_id', 'business_unit']
    ordering = ['business_unit', 'account_name']
    actions = ['export_selected_accounts']

    def get_urls(self):
        """Add custom URLs for bulk upload"""
        urls = super().get_urls()
        custom_urls = [
            path('bulk-upload/', self.admin_site.admin_view(self.bulk_upload_view), name='inventory_awsaccount_bulk_upload'),
        ]
        return custom_urls + urls

    def changelist_view(self, request, extra_context=None):
        """Add bulk upload button to changelist view"""
        extra_context = extra_context or {}
        extra_context['bulk_upload_url'] = 'bulk-upload/'
        return super().changelist_view(request, extra_context)

    def bulk_upload_view(self, request):
        """Handle bulk upload of AWS accounts from CSV"""
        if request.method == 'POST':
            csv_file = request.FILES.get('csv_file')

            if not csv_file:
                messages.error(request, 'Please select a CSV file to upload.')
                return render(request, 'admin/inventory/awsaccount/bulk_upload.html')

            if not csv_file.name.endswith('.csv'):
                messages.error(request, 'Please upload a CSV file.')
                return render(request, 'admin/inventory/awsaccount/bulk_upload.html')

            try:
                # Read and process CSV file
                decoded_file = csv_file.read().decode('utf-8')
                csv_data = csv.DictReader(io.StringIO(decoded_file))

                created_count = 0
                updated_count = 0
                error_count = 0
                errors = []

                for row_num, row in enumerate(csv_data, start=2):  # Start at 2 because row 1 is header
                    try:
                        # Clean and validate data
                        account_id = str(row.get('account_id', '')).strip()
                        account_name = str(row.get('account_name', '')).strip()
                        region = str(row.get('region', '')).strip()
                        business_unit = str(row.get('bu', '')).strip()

                        # Validate required fields
                        if not account_id:
                            errors.append(f"Row {row_num}: Account ID is required")
                            error_count += 1
                            continue

                        if not account_name:
                            errors.append(f"Row {row_num}: Account name is required")
                            error_count += 1
                            continue

                        if not region:
                            errors.append(f"Row {row_num}: Region is required")
                            error_count += 1
                            continue

                        if not business_unit:
                            errors.append(f"Row {row_num}: Business unit is required")
                            error_count += 1
                            continue

                        # Validate account ID format (12 digits)
                        if not account_id.isdigit() or len(account_id) != 12:
                            errors.append(f"Row {row_num}: Account ID must be 12 digits")
                            error_count += 1
                            continue

                        # Create or update account
                        account, created = AWSAccount.objects.update_or_create(
                            account_id=account_id,
                            defaults={
                                'account_name': account_name,
                                'region': region,
                                'business_unit': business_unit,
                                'is_active': True
                            }
                        )

                        if created:
                            created_count += 1
                        else:
                            updated_count += 1

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")
                        error_count += 1

                # Show results
                if created_count > 0:
                    messages.success(request, f'Successfully created {created_count} new AWS accounts.')

                if updated_count > 0:
                    messages.info(request, f'Updated {updated_count} existing AWS accounts.')

                if error_count > 0:
                    error_message = f'Failed to process {error_count} rows:\n' + '\n'.join(errors[:10])
                    if len(errors) > 10:
                        error_message += f'\n... and {len(errors) - 10} more errors.'
                    messages.error(request, error_message)

                if created_count > 0 or updated_count > 0:
                    return HttpResponseRedirect('../')

            except Exception as e:
                messages.error(request, f'Error processing CSV file: {str(e)}')

        return render(request, 'admin/inventory/awsaccount/bulk_upload.html')

    def export_selected_accounts(self, request, queryset):
        """Export selected accounts to CSV"""
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="aws_accounts.csv"'

        writer = csv.writer(response)
        writer.writerow(['account_id', 'account_name', 'region', 'bu'])

        for account in queryset:
            writer.writerow([
                account.account_id,
                account.account_name,
                account.region,
                account.business_unit
            ])

        return response

    export_selected_accounts.short_description = "Export selected accounts to CSV"


@admin.register(EC2Instance)
class EC2InstanceAdmin(admin.ModelAdmin):
    list_display = ['instance_id', 'name', 'instance_type', 'state', 'account', 'env_tag', 'uai_tag', 'cto_cloud_ops_managed', 'last_updated']
    list_filter = ['state', 'instance_type', 'account__business_unit', 'env_tag', 'uai_tag', 'cto_cloud_ops_managed']
    search_fields = ['instance_id', 'name', 'private_ip_address', 'ami_id', 'subnet_id']
    ordering = ['-last_updated']
    readonly_fields = ['created_at', 'last_updated']
    filter_horizontal = ['tags']

    fieldsets = (
        ('Basic Information', {
            'fields': ('account', 'instance_id', 'name', 'state', 'instance_type')
        }),
        ('Specifications', {
            'fields': ('vcpu_count', 'memory_gb', 'ami_id', 'os_information', 'platform_type')
        }),
        ('Network & Security', {
            'fields': ('private_ip_address', 'private_dns_name', 'subnet_id', 'subnet_name', 'security_groups', 'instance_profile')
        }),
        ('Storage', {
            'fields': ('root_volume_id', 'root_volume_size_gb', 'data_disk_count')
        }),
        ('Tags', {
            'fields': ('env_tag', 'uai_tag', 'patch_tag', 'app_env_cfg_id', 'maintenance_schedule',
                      'schedule_tag', 'backup_tag', 'cto_cloud_ops_managed', 'patch_provider',
                      'patch_group', 'patch_exempt')
        }),
        ('Flattened Tags', {
            'fields': ('tags', 'all_tags_raw'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'last_updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SSMStatus)
class SSMStatusAdmin(admin.ModelAdmin):
    list_display = ['instance', 'ping_status', 'agent_version', 'is_latest_version', 'last_ping_datetime', 'last_checked']
    list_filter = ['ping_status', 'is_latest_version', 'ssm_configured', 'platform_type']
    search_fields = ['instance__instance_id', 'instance__name', 'agent_version']
    ordering = ['-last_checked']
    readonly_fields = ['last_checked', 'created_at']


@admin.register(InventoryRefreshLog)
class InventoryRefreshLogAdmin(admin.ModelAdmin):
    list_display = ['account', 'status', 'started_at', 'completed_at', 'instances_processed', 'errors_count']
    list_filter = ['status', 'account__business_unit']
    search_fields = ['account__account_name', 'account__account_id']
    ordering = ['-started_at']
    readonly_fields = ['started_at', 'completed_at']
