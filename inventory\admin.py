from django.contrib import admin
from .models import AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog, InstanceTag


@admin.register(InstanceTag)
class InstanceTagAdmin(admin.ModelAdmin):
    list_display = ['key', 'value', 'instances_count', 'created_at']
    list_filter = ['key']
    search_fields = ['key', 'value']
    ordering = ['key', 'value']

    def instances_count(self, obj):
        return obj.instances.count()
    instances_count.short_description = 'Instances Count'


@admin.register(AWSAccount)
class AWSAccountAdmin(admin.ModelAdmin):
    list_display = ['account_name', 'account_id', 'business_unit', 'region', 'is_active', 'created_at']
    list_filter = ['business_unit', 'region', 'is_active']
    search_fields = ['account_name', 'account_id', 'business_unit']
    ordering = ['business_unit', 'account_name']


@admin.register(EC2Instance)
class EC2InstanceAdmin(admin.ModelAdmin):
    list_display = ['instance_id', 'name', 'instance_type', 'state', 'account', 'env_tag', 'uai_tag', 'cto_cloud_ops_managed', 'last_updated']
    list_filter = ['state', 'instance_type', 'account__business_unit', 'env_tag', 'uai_tag', 'cto_cloud_ops_managed']
    search_fields = ['instance_id', 'name', 'private_ip_address', 'ami_id', 'subnet_id']
    ordering = ['-last_updated']
    readonly_fields = ['created_at', 'last_updated']
    filter_horizontal = ['tags']

    fieldsets = (
        ('Basic Information', {
            'fields': ('account', 'instance_id', 'name', 'state', 'instance_type')
        }),
        ('Specifications', {
            'fields': ('vcpu_count', 'memory_gb', 'ami_id', 'os_information', 'platform_type')
        }),
        ('Network & Security', {
            'fields': ('private_ip_address', 'private_dns_name', 'subnet_id', 'subnet_name', 'security_groups', 'instance_profile')
        }),
        ('Storage', {
            'fields': ('root_volume_id', 'root_volume_size_gb', 'data_disk_count')
        }),
        ('Tags', {
            'fields': ('env_tag', 'uai_tag', 'patch_tag', 'app_env_cfg_id', 'maintenance_schedule',
                      'schedule_tag', 'backup_tag', 'cto_cloud_ops_managed', 'patch_provider',
                      'patch_group', 'patch_exempt')
        }),
        ('Flattened Tags', {
            'fields': ('tags', 'all_tags_raw'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'last_updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SSMStatus)
class SSMStatusAdmin(admin.ModelAdmin):
    list_display = ['instance', 'ping_status', 'agent_version', 'is_latest_version', 'last_ping_datetime', 'last_checked']
    list_filter = ['ping_status', 'is_latest_version', 'ssm_configured', 'platform_type']
    search_fields = ['instance__instance_id', 'instance__name', 'agent_version']
    ordering = ['-last_checked']
    readonly_fields = ['last_checked', 'created_at']


@admin.register(InventoryRefreshLog)
class InventoryRefreshLogAdmin(admin.ModelAdmin):
    list_display = ['account', 'status', 'started_at', 'completed_at', 'instances_processed', 'errors_count']
    list_filter = ['status', 'account__business_unit']
    search_fields = ['account__account_name', 'account__account_id']
    ordering = ['-started_at']
    readonly_fields = ['started_at', 'completed_at']
