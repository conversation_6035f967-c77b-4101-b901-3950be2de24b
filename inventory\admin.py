from django.contrib import admin
from .models import AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog


@admin.register(AWSAccount)
class AWSAccountAdmin(admin.ModelAdmin):
    list_display = ['account_name', 'account_id', 'business_unit', 'region', 'is_active', 'created_at']
    list_filter = ['business_unit', 'region', 'is_active']
    search_fields = ['account_name', 'account_id', 'business_unit']
    ordering = ['business_unit', 'account_name']


@admin.register(EC2Instance)
class EC2InstanceAdmin(admin.ModelAdmin):
    list_display = ['instance_id', 'name', 'instance_type', 'state', 'account', 'env_tag', 'last_updated']
    list_filter = ['state', 'instance_type', 'account__business_unit', 'env_tag']
    search_fields = ['instance_id', 'name', 'private_ip_address']
    ordering = ['-last_updated']
    readonly_fields = ['created_at', 'last_updated']


@admin.register(SSMStatus)
class SSMStatusAdmin(admin.ModelAdmin):
    list_display = ['instance', 'ping_status', 'agent_version', 'is_latest_version', 'last_ping_datetime']
    list_filter = ['ping_status', 'is_latest_version', 'ssm_configured']
    search_fields = ['instance__instance_id', 'instance__name']
    ordering = ['-last_checked']


@admin.register(InventoryRefreshLog)
class InventoryRefreshLogAdmin(admin.ModelAdmin):
    list_display = ['account', 'status', 'started_at', 'completed_at', 'instances_processed', 'errors_count']
    list_filter = ['status', 'account__business_unit']
    search_fields = ['account__account_name', 'account__account_id']
    ordering = ['-started_at']
    readonly_fields = ['started_at', 'completed_at']
