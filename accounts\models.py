from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models
from django.utils import timezone
from django.core.validators import RegexValidator


class UserRole(models.Model):
    """Custom user roles with specific permissions"""
    ROLE_CHOICES = [
        ('admin', 'Admin'),
        ('automation_user', 'Automation User'),
        ('reader', 'Reader'),
    ]

    name = models.CharField(max_length=50, choices=ROLE_CHOICES, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'

    def __str__(self):
        return self.display_name

    @classmethod
    def get_default_roles(cls):
        """Get or create default roles"""
        roles_data = [
            {
                'name': 'admin',
                'display_name': 'Admin',
                'description': 'Full access to all features including user management, system configuration, and all operations.'
            },
            {
                'name': 'automation_user',
                'display_name': 'Automation User',
                'description': 'Read access to all inventory and reports, plus execute access for deployment tasks and automation workflows.'
            },
            {
                'name': 'reader',
                'display_name': 'Reader',
                'description': 'Read-only access to inventory, automation reports, and decommission reports.'
            }
        ]
        
        created_roles = []
        for role_data in roles_data:
            role, created = cls.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            created_roles.append(role)
        
        return created_roles


class CustomUser(AbstractUser):
    """Extended user model with additional fields for enterprise features"""
    
    # Additional user information
    employee_id = models.CharField(
        max_length=20, 
        blank=True, 
        null=True,
        validators=[RegexValidator(r'^[A-Za-z0-9-_]+$', 'Employee ID can only contain letters, numbers, hyphens, and underscores.')]
    )
    department = models.CharField(max_length=100, blank=True)
    business_unit = models.CharField(max_length=100, blank=True)
    manager_email = models.EmailField(blank=True)
    phone_number = models.CharField(
        max_length=20, 
        blank=True,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'Phone number must be entered in the format: "+*********". Up to 15 digits allowed.')]
    )
    
    # Role assignment
    user_role = models.ForeignKey(
        UserRole, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='users'
    )
    
    # SAML SSO fields for future implementation
    saml_user_id = models.CharField(max_length=255, blank=True, null=True, unique=True)
    saml_attributes = models.JSONField(default=dict, blank=True)
    is_saml_user = models.BooleanField(default=False)
    
    # Account status and tracking
    is_approved = models.BooleanField(default=False)
    approved_by = models.ForeignKey(
        'self', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='approved_users'
    )
    approved_at = models.DateTimeField(null=True, blank=True)
    
    # Login tracking
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    login_count = models.PositiveIntegerField(default=0)
    failed_login_attempts = models.PositiveIntegerField(default=0)
    last_failed_login = models.DateTimeField(null=True, blank=True)
    account_locked_until = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['username']
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.get_full_name()} ({self.username})" if self.get_full_name() else self.username

    def get_role_display(self):
        """Get user role display name"""
        return self.user_role.display_name if self.user_role else 'No Role'

    def has_role(self, role_name):
        """Check if user has specific role"""
        return self.user_role and self.user_role.name == role_name

    def is_admin(self):
        """Check if user is admin"""
        return self.has_role('admin') or self.is_superuser

    def is_automation_user(self):
        """Check if user is automation user"""
        return self.has_role('automation_user')

    def is_reader(self):
        """Check if user is reader"""
        return self.has_role('reader')

    def can_execute_automation(self):
        """Check if user can execute automation tasks"""
        return self.is_admin() or self.is_automation_user()

    def can_manage_users(self):
        """Check if user can manage other users"""
        return self.is_admin()

    def can_view_inventory(self):
        """Check if user can view inventory"""
        return self.is_active and self.is_approved

    def can_export_data(self):
        """Check if user can export data"""
        return self.is_active and self.is_approved

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.account_locked_until = timezone.now() + timezone.timedelta(minutes=duration_minutes)
        self.save()

    def unlock_account(self):
        """Unlock account"""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save()

    def record_login(self, ip_address=None):
        """Record successful login"""
        self.last_login = timezone.now()
        self.last_login_ip = ip_address
        self.login_count += 1
        self.failed_login_attempts = 0
        self.account_locked_until = None
        self.save()

    def record_failed_login(self):
        """Record failed login attempt"""
        self.failed_login_attempts += 1
        self.last_failed_login = timezone.now()
        
        # Lock account after 5 failed attempts
        if self.failed_login_attempts >= 5:
            self.lock_account(30)  # Lock for 30 minutes
        
        self.save()

    def approve_user(self, approved_by_user):
        """Approve user account"""
        self.is_approved = True
        self.approved_by = approved_by_user
        self.approved_at = timezone.now()
        self.save()

    def get_permissions_summary(self):
        """Get summary of user permissions based on role"""
        if not self.user_role:
            return []
        
        permissions = []
        
        if self.is_admin():
            permissions = [
                'Full system access',
                'User management',
                'System configuration',
                'All inventory operations',
                'All automation operations',
                'All reports and exports'
            ]
        elif self.is_automation_user():
            permissions = [
                'Read all inventory',
                'Execute deployment tasks',
                'Run automation workflows',
                'View automation reports',
                'View decommission reports',
                'Export data'
            ]
        elif self.is_reader():
            permissions = [
                'Read-only inventory access',
                'View automation reports',
                'View decommission reports',
                'Export data (read-only)'
            ]
        
        return permissions


class UserSession(models.Model):
    """Track user sessions for security and auditing"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    login_time = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    logout_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-login_time']
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'

    def __str__(self):
        return f"{self.user.username} - {self.login_time}"

    def end_session(self):
        """End the session"""
        self.is_active = False
        self.logout_time = timezone.now()
        self.save()


class UserActivity(models.Model):
    """Track user activities for auditing"""
    ACTION_CHOICES = [
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('view_inventory', 'View Inventory'),
        ('export_data', 'Export Data'),
        ('execute_automation', 'Execute Automation'),
        ('manage_users', 'Manage Users'),
        ('system_config', 'System Configuration'),
        ('failed_login', 'Failed Login'),
        ('account_locked', 'Account Locked'),
    ]

    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='activities')
    action = models.CharField(max_length=50, choices=ACTION_CHOICES)
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    additional_data = models.JSONField(default=dict, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'User Activity'
        verbose_name_plural = 'User Activities'

    def __str__(self):
        return f"{self.user.username} - {self.get_action_display()} - {self.timestamp}"


class SAMLConfiguration(models.Model):
    """SAML SSO configuration for future implementation"""
    name = models.CharField(max_length=100, unique=True)
    entity_id = models.URLField()
    sso_url = models.URLField()
    slo_url = models.URLField(blank=True)
    x509_cert = models.TextField()
    
    # Attribute mapping
    username_attribute = models.CharField(max_length=100, default='username')
    email_attribute = models.CharField(max_length=100, default='email')
    first_name_attribute = models.CharField(max_length=100, default='first_name')
    last_name_attribute = models.CharField(max_length=100, default='last_name')
    employee_id_attribute = models.CharField(max_length=100, default='employee_id')
    department_attribute = models.CharField(max_length=100, default='department')
    business_unit_attribute = models.CharField(max_length=100, default='business_unit')
    
    # Role mapping
    role_attribute = models.CharField(max_length=100, default='role')
    admin_role_values = models.JSONField(default=list, blank=True)
    automation_user_role_values = models.JSONField(default=list, blank=True)
    reader_role_values = models.JSONField(default=list, blank=True)
    
    # Settings
    is_active = models.BooleanField(default=False)
    auto_create_users = models.BooleanField(default=True)
    auto_approve_users = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'SAML Configuration'
        verbose_name_plural = 'SAML Configurations'

    def __str__(self):
        return self.name
