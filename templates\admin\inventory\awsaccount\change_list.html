{% extends "admin/change_list.html" %}
{% load i18n admin_urls static %}

{% block extrahead %}
{{ block.super }}
<style>
    .bulk-upload-btn {
        background: #28a745;
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
        margin-left: 10px;
        font-size: 13px;
        transition: background 0.3s ease;
    }
    
    .bulk-upload-btn:hover {
        background: #218838;
        color: white;
        text-decoration: none;
    }
    
    .bulk-upload-btn i {
        margin-right: 5px;
    }
    
    .actions-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }
</style>
{% endblock %}

{% block object-tools %}
<ul class="object-tools">
    {% block object-tools-items %}
        <li>
            <a href="{% url 'admin:inventory_awsaccount_bulk_upload' %}" class="bulk-upload-btn">
                <i class="fas fa-cloud-upload-alt"></i>
                Bulk Upload CSV
            </a>
        </li>
        {% if has_add_permission %}
            <li>
                <a href="{% url 'admin:inventory_awsaccount_add' %}" class="addlink">
                    {% trans 'Add AWS Account' %}
                </a>
            </li>
        {% endif %}
    {% endblock %}
</ul>
{% endblock %}

{% block content_title %}
<h1>
    AWS Accounts
    <span style="font-size: 14px; color: #666; font-weight: normal;">
        ({{ cl.result_count }} total)
    </span>
</h1>
{% endblock %}

{% block result_list %}
{{ block.super }}

{% if cl.result_count > 0 %}
<div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007cba;">
    <h3 style="margin-top: 0; color: #007cba;">
        <i class="fas fa-info-circle"></i> Bulk Operations
    </h3>
    <p style="margin-bottom: 10px;">
        <strong>Upload CSV:</strong> Use the "Bulk Upload CSV" button above to import multiple AWS accounts at once.
    </p>
    <p style="margin-bottom: 10px;">
        <strong>Export CSV:</strong> Select accounts below and use the "Export selected accounts to CSV" action.
    </p>
    <p style="margin-bottom: 0; font-size: 12px; color: #666;">
        CSV Format: account_id, account_name, region, bu
    </p>
</div>
{% endif %}
{% endblock %}
