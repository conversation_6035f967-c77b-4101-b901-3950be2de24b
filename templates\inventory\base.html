<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AWS Inventory Management{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #017054;
        }
        .sidebar .nav-link {
            color: white;
            padding: 15px 20px;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .main-content {
            padding: 20px;
        }
        .stats-card {
            border-left: 4px solid #017054;
        }
        .loading {
            display: none;
        }
        .alert-custom {
            margin-top: 10px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="sidebar-sticky">
                    <div class="text-center py-4">
                        <h4 class="text-white">AWS Inventory</h4>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" href="{% url 'inventory:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'ec2_instances' %}active{% endif %}" href="{% url 'inventory:ec2_instances' %}">
                                <i class="fas fa-server me-2"></i>
                                EC2 Instances
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'ssm_status' %}active{% endif %}" href="{% url 'inventory:ssm_status' %}">
                                <i class="fas fa-heartbeat me-2"></i>
                                SSM Status
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'accounts' %}active{% endif %}" href="{% url 'inventory:accounts' %}">
                                <i class="fas fa-users me-2"></i>
                                AWS Accounts
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}Dashboard{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block page_actions %}
                        <button type="button" class="btn btn-success" onclick="refreshAllInventory()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Refresh All
                        </button>
                        {% endblock %}
                    </div>
                </div>

                <!-- Alert container -->
                <div id="alert-container"></div>

                <!-- Loading indicator -->
                <div class="loading text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading data...</p>
                </div>

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // CSRF token for AJAX requests
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        const csrftoken = getCookie('csrftoken');

        // Show alert function
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show alert-custom" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('#alert-container').html(alertHtml);
        }

        // Show/hide loading
        function showLoading() {
            $('.loading').show();
        }

        function hideLoading() {
            $('.loading').hide();
        }

        // Refresh all inventory
        function refreshAllInventory() {
            if (confirm('This will refresh inventory for all accounts. This may take several minutes. Continue?')) {
                showLoading();
                
                $.ajax({
                    url: '/api/refresh-all/',
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrftoken
                    },
                    success: function(response) {
                        hideLoading();
                        showAlert(response.message, 'success');
                        // Reload page data if we're on a data page
                        if (typeof loadData === 'function') {
                            loadData();
                        }
                    },
                    error: function(xhr) {
                        hideLoading();
                        const response = xhr.responseJSON || {};
                        showAlert(response.message || 'Failed to refresh inventory', 'danger');
                    }
                });
            }
        }

        // Format date function
        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleString();
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
