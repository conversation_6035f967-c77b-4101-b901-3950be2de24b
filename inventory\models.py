from django.db import models
from django.utils import timezone


class AWSAccount(models.Model):
    """Model to store AWS account information"""
    account_id = models.CharField(max_length=12, unique=True)
    account_name = models.CharField(max_length=255, blank=True)
    region = models.CharField(max_length=50)
    business_unit = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['business_unit', 'account_name']

    def __str__(self):
        return f"{self.account_name} ({self.account_id}) - {self.business_unit}"


class EC2Instance(models.Model):
    """Model to store EC2 instance information"""
    STATE_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('shutting-down', 'Shutting Down'),
        ('terminated', 'Terminated'),
        ('stopping', 'Stopping'),
        ('stopped', 'Stopped'),
    ]

    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='ec2_instances')
    instance_id = models.CharField(max_length=20)
    instance_type = models.CharField(max_length=50)
    state = models.CharField(max_length=20, choices=STATE_CHOICES)
    name = models.CharField(max_length=255, blank=True)
    private_ip_address = models.GenericIPAddressField(null=True, blank=True)
    private_dns_name = models.CharField(max_length=255, blank=True)

    # Instance specifications
    vcpu_count = models.IntegerField(null=True, blank=True)
    memory_gb = models.FloatField(null=True, blank=True)

    # Storage information
    root_volume_id = models.CharField(max_length=50, blank=True)
    root_volume_size_gb = models.IntegerField(null=True, blank=True)
    data_disk_count = models.IntegerField(default=0)

    # OS Information
    os_information = models.CharField(max_length=255, blank=True)
    platform_type = models.CharField(max_length=50, blank=True)

    # Tags
    env_tag = models.CharField(max_length=100, blank=True)
    uai_tag = models.CharField(max_length=100, blank=True)
    patch_tag = models.CharField(max_length=100, blank=True)
    app_env_cfg_id = models.CharField(max_length=100, blank=True)
    maintenance_schedule = models.CharField(max_length=100, blank=True)
    schedule_tag = models.CharField(max_length=100, blank=True)
    backup_tag = models.CharField(max_length=100, blank=True)
    cto_cloud_ops_managed = models.CharField(max_length=100, default='NotAssigned')
    patch_provider = models.CharField(max_length=100, default='NotAssigned')
    patch_group = models.CharField(max_length=100, default='NotAssigned')
    patch_exempt = models.CharField(max_length=100, default='NotAssigned')
    all_tags = models.TextField(blank=True)

    # Metadata
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['account', 'instance_id']
        ordering = ['-last_updated']

    def __str__(self):
        return f"{self.name or self.instance_id} ({self.instance_type}) - {self.account.account_name}"


class SSMStatus(models.Model):
    """Model to store SSM agent status information"""
    PING_STATUS_CHOICES = [
        ('Online', 'Online'),
        ('Connection Lost', 'Connection Lost'),
        ('Inactive', 'Inactive'),
    ]

    instance = models.OneToOneField(EC2Instance, on_delete=models.CASCADE, related_name='ssm_status')
    ping_status = models.CharField(max_length=20, choices=PING_STATUS_CHOICES, blank=True)
    agent_version = models.CharField(max_length=50, blank=True)
    is_latest_version = models.BooleanField(null=True, blank=True)
    last_ping_datetime = models.DateTimeField(null=True, blank=True)
    platform_name = models.CharField(max_length=100, blank=True)
    platform_type = models.CharField(max_length=50, blank=True)

    # Status tracking
    ssm_configured = models.BooleanField(default=False)
    last_checked = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-last_checked']

    def __str__(self):
        return f"SSM Status for {self.instance.instance_id} - {self.ping_status}"


class InventoryRefreshLog(models.Model):
    """Model to track inventory refresh operations"""
    STATUS_CHOICES = [
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='refresh_logs')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='running')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    instances_processed = models.IntegerField(default=0)
    errors_count = models.IntegerField(default=0)
    error_details = models.TextField(blank=True)

    class Meta:
        ordering = ['-started_at']

    def __str__(self):
        return f"Refresh {self.account.account_name} - {self.status} ({self.started_at})"
