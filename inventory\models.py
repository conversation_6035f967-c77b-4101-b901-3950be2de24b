from django.db import models
from django.utils import timezone


class AWSAccount(models.Model):
    """Model to store AWS account information"""
    account_id = models.CharField(max_length=12, unique=True)
    account_name = models.CharField(max_length=255, blank=True)
    region = models.CharField(max_length=50)
    business_unit = models.CharField(max_length=100)  # BU
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['business_unit', 'account_name']

    def __str__(self):
        return f"{self.account_name} ({self.account_id}) - {self.business_unit}"


class InstanceTag(models.Model):
    """Model to store flattened instance tags as key-value pairs"""
    key = models.CharField(max_length=255, db_index=True)
    value = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['key', 'value']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['key', 'value']),
        ]

    def __str__(self):
        return f"{self.key}={self.value}"


class EC2Instance(models.Model):
    """Model to store EC2 instance information with all required fields"""
    STATE_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('shutting-down', 'Shutting Down'),
        ('terminated', 'Terminated'),
        ('stopping', 'Stopping'),
        ('stopped', 'Stopped'),
    ]

    # Core identification
    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='ec2_instances')
    instance_id = models.CharField(max_length=20, db_index=True)
    name = models.CharField(max_length=255, blank=True, db_index=True)
    state = models.CharField(max_length=20, choices=STATE_CHOICES, db_index=True)

    # Instance specifications
    instance_type = models.CharField(max_length=50, db_index=True)
    vcpu_count = models.IntegerField(null=True, blank=True)  # V CPU
    memory_gb = models.FloatField(null=True, blank=True)  # Memory/Ram (GB)

    # Network information
    private_ip_address = models.GenericIPAddressField(null=True, blank=True, db_index=True)
    private_dns_name = models.CharField(max_length=255, blank=True)
    subnet_id = models.CharField(max_length=50, blank=True)
    subnet_name = models.CharField(max_length=255, blank=True)

    # Security and access
    security_groups = models.TextField(blank=True)  # JSON array of security group IDs/names
    instance_profile = models.CharField(max_length=255, blank=True)

    # Storage information
    root_volume_id = models.CharField(max_length=50, blank=True)  # Root EBS Volume ID
    root_volume_size_gb = models.IntegerField(null=True, blank=True)  # Root EBS Volume Size (GB)
    data_disk_count = models.IntegerField(default=0)  # Data Disk Count

    # OS and AMI information
    os_information = models.CharField(max_length=255, blank=True)  # OS Information
    ami_id = models.CharField(max_length=50, blank=True)  # AMI
    platform_type = models.CharField(max_length=50, blank=True)

    # Common tags as direct fields for quick access
    env_tag = models.CharField(max_length=100, blank=True, db_index=True)  # Env Tag
    uai_tag = models.CharField(max_length=100, blank=True, db_index=True)  # Uai Tag
    patch_tag = models.CharField(max_length=100, blank=True)  # Patch Tag
    app_env_cfg_id = models.CharField(max_length=100, blank=True)  # AppEnvCfgID Tag
    maintenance_schedule = models.CharField(max_length=100, blank=True)  # MaintenanceSchedule Tag
    schedule_tag = models.CharField(max_length=100, blank=True)  # Schedule Tag
    backup_tag = models.CharField(max_length=100, blank=True)  # Backup Tag
    cto_cloud_ops_managed = models.CharField(max_length=100, blank=True, db_index=True)  # CTOCloudOpsManaged Tag
    patch_provider = models.CharField(max_length=100, blank=True)  # PatchProvider Tag
    patch_group = models.CharField(max_length=100, blank=True)  # PatchGroup Tag
    patch_exempt = models.CharField(max_length=100, blank=True)  # PatchExempt Tag

    # Flattened tags relationship
    tags = models.ManyToManyField(InstanceTag, blank=True, related_name='instances')
    all_tags_raw = models.TextField(blank=True)  # Raw tags string for backup

    # Metadata
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['account', 'instance_id']
        ordering = ['-last_updated']
        indexes = [
            models.Index(fields=['account', 'instance_id']),
            models.Index(fields=['state', 'instance_type']),
            models.Index(fields=['env_tag', 'uai_tag']),
            models.Index(fields=['cto_cloud_ops_managed']),
        ]

    def __str__(self):
        return f"{self.name or self.instance_id} ({self.instance_type}) - {self.account.account_name}"

    def get_business_unit(self):
        """Get business unit from account"""
        return self.account.business_unit

    def get_account_name(self):
        """Get account name"""
        return self.account.account_name

    def get_account_id(self):
        """Get account ID"""
        return self.account.account_id

    def get_region(self):
        """Get region"""
        return self.account.region


class SSMStatus(models.Model):
    """Model to store SSM agent status information"""
    PING_STATUS_CHOICES = [
        ('Online', 'Online'),
        ('Connection Lost', 'Connection Lost'),
        ('Inactive', 'Inactive'),
        ('Unknown', 'Unknown'),
    ]

    instance = models.OneToOneField(EC2Instance, on_delete=models.CASCADE, related_name='ssm_status')
    ping_status = models.CharField(max_length=20, choices=PING_STATUS_CHOICES, blank=True, db_index=True)  # SSM status
    agent_version = models.CharField(max_length=50, blank=True)  # SSM Agent version
    is_latest_version = models.BooleanField(null=True, blank=True)  # is SSM agent latest
    last_ping_datetime = models.DateTimeField(null=True, blank=True)
    platform_name = models.CharField(max_length=100, blank=True)
    platform_type = models.CharField(max_length=50, blank=True)

    # Status tracking
    ssm_configured = models.BooleanField(default=False)
    last_checked = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-last_checked']
        indexes = [
            models.Index(fields=['ping_status']),
            models.Index(fields=['is_latest_version']),
        ]

    def __str__(self):
        return f"SSM Status for {self.instance.instance_id} - {self.ping_status}"


class InventoryRefreshLog(models.Model):
    """Model to track inventory refresh operations"""
    STATUS_CHOICES = [
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='refresh_logs')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='running')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    instances_processed = models.IntegerField(default=0)
    errors_count = models.IntegerField(default=0)
    error_details = models.TextField(blank=True)

    class Meta:
        ordering = ['-started_at']

    def __str__(self):
        return f"Refresh {self.account.account_name} - {self.status} ({self.started_at})"
