{% extends 'inventory/base.html' %}

{% block title %}EC2 Instances - AWS Inventory{% endblock %}
{% block page_title %}EC2 Instances{% endblock %}

{% block page_actions %}
<div class="btn-group me-2">
    <button type="button" class="btn btn-outline-primary" onclick="exportToExcel()">
        <i class="fas fa-file-excel me-1"></i>
        Export Excel
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="loadInstances()">
        <i class="fas fa-sync-alt me-1"></i>
        Refresh
    </button>
</div>
<button type="button" class="btn btn-success" onclick="refreshAllInventory()">
    <i class="fas fa-sync-alt me-1"></i>
    Refresh All
</button>
{% endblock %}

{% block content %}
<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="card-title mb-0">Filters</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="filter-account" class="form-label">Account</label>
                <select class="form-select" id="filter-account">
                    <option value="">All Accounts</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="filter-state" class="form-label">State</label>
                <select class="form-select" id="filter-state">
                    <option value="">All States</option>
                    <option value="running">Running</option>
                    <option value="stopped">Stopped</option>
                    <option value="pending">Pending</option>
                    <option value="stopping">Stopping</option>
                    <option value="terminated">Terminated</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="filter-env" class="form-label">Environment</label>
                <select class="form-select" id="filter-env">
                    <option value="">All Environments</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="filter-search" class="form-label">Search</label>
                <input type="text" class="form-control" id="filter-search" placeholder="Instance ID, Name, IP...">
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <button type="button" class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-filter me-1"></i>
                    Apply Filters
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i>
                    Clear
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Instances Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="card-title mb-0">EC2 Instances</h6>
        <span class="badge bg-primary" id="instance-count">0 instances</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="instances-table">
                <thead>
                    <tr>
                        <th>Instance ID</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>State</th>
                        <th>Private IP</th>
                        <th>OS</th>
                        <th>Account</th>
                        <th>Environment</th>
                        <th>SSM Status</th>
                        <th>Last Updated</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Instances pagination">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Pagination will be generated dynamically -->
            </ul>
        </nav>
    </div>
</div>

<!-- Instance Detail Modal -->
<div class="modal fade" id="instanceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Instance Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="instance-details">
                <!-- Instance details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let currentFilters = {};

    function loadInstances(page = 1) {
        showLoading();
        
        let url = `/api/ec2-instances/?page=${page}`;
        
        // Add filters to URL
        Object.keys(currentFilters).forEach(key => {
            if (currentFilters[key]) {
                url += `&${key}=${encodeURIComponent(currentFilters[key])}`;
            }
        });
        
        $.ajax({
            url: url,
            method: 'GET',
            success: function(data) {
                updateInstancesTable(data.results);
                updatePagination(data);
                updateInstanceCount(data.count);
                hideLoading();
            },
            error: function(xhr) {
                hideLoading();
                showAlert('Failed to load instances', 'danger');
            }
        });
    }

    function updateInstancesTable(instances) {
        const tbody = $('#instances-table tbody');
        tbody.empty();
        
        if (instances && instances.length > 0) {
            instances.forEach(function(instance) {
                const row = `
                    <tr>
                        <td><code>${instance.instance_id}</code></td>
                        <td>${instance.name || '-'}</td>
                        <td>${instance.instance_type}</td>
                        <td><span class="badge bg-${getStateColor(instance.state)}">${instance.state}</span></td>
                        <td>${instance.private_ip_address || '-'}</td>
                        <td>${instance.os_information || '-'}</td>
                        <td>${instance.account_name}</td>
                        <td>${instance.env_tag || '-'}</td>
                        <td><span class="badge bg-${getSSMColor(instance.ssm_ping_status)}">${instance.ssm_ping_status || 'Unknown'}</span></td>
                        <td>${formatDate(instance.last_updated)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewInstanceDetails(${instance.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="11" class="text-center">No instances found</td></tr>');
        }
    }

    function updatePagination(data) {
        const pagination = $('#pagination');
        pagination.empty();
        
        if (data.count > 50) { // Assuming page size is 50
            const totalPages = Math.ceil(data.count / 50);
            
            // Previous button
            if (data.previous) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadInstances(${currentPage - 1})">Previous</a>
                    </li>
                `);
            }
            
            // Page numbers (show max 5 pages around current)
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadInstances(${i})">${i}</a>
                    </li>
                `);
            }
            
            // Next button
            if (data.next) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadInstances(${currentPage + 1})">Next</a>
                    </li>
                `);
            }
        }
    }

    function updateInstanceCount(count) {
        $('#instance-count').text(`${count} instances`);
    }

    function loadFilterOptions() {
        // Load accounts for filter
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            success: function(data) {
                const accountSelect = $('#filter-account');
                data.results.forEach(function(account) {
                    accountSelect.append(`<option value="${account.account_id}">${account.account_name}</option>`);
                });
            }
        });
        
        // Load environments (this would need a separate endpoint or be extracted from instances)
        // For now, we'll populate common environments
        const envSelect = $('#filter-env');
        ['dev', 'test', 'staging', 'prod', 'production'].forEach(function(env) {
            envSelect.append(`<option value="${env}">${env}</option>`);
        });
    }

    function applyFilters() {
        currentFilters = {
            account_id: $('#filter-account').val(),
            state: $('#filter-state').val(),
            env_tag: $('#filter-env').val(),
            search: $('#filter-search').val()
        };
        currentPage = 1;
        loadInstances(1);
    }

    function clearFilters() {
        $('#filter-account').val('');
        $('#filter-state').val('');
        $('#filter-env').val('');
        $('#filter-search').val('');
        currentFilters = {};
        currentPage = 1;
        loadInstances(1);
    }

    function exportToExcel() {
        showLoading();
        
        let url = '/api/ec2-instances/export_excel/';
        
        // Add current filters to export
        const params = new URLSearchParams(currentFilters);
        if (params.toString()) {
            url += '?' + params.toString();
        }
        
        window.location.href = url;
        hideLoading();
    }

    function viewInstanceDetails(instanceId) {
        $.ajax({
            url: `/api/ec2-instances/${instanceId}/`,
            method: 'GET',
            success: function(instance) {
                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Basic Information</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Instance ID:</strong></td><td>${instance.instance_id}</td></tr>
                                <tr><td><strong>Name:</strong></td><td>${instance.name || '-'}</td></tr>
                                <tr><td><strong>Type:</strong></td><td>${instance.instance_type}</td></tr>
                                <tr><td><strong>State:</strong></td><td><span class="badge bg-${getStateColor(instance.state)}">${instance.state}</span></td></tr>
                                <tr><td><strong>Private IP:</strong></td><td>${instance.private_ip_address || '-'}</td></tr>
                                <tr><td><strong>vCPU:</strong></td><td>${instance.vcpu_count || '-'}</td></tr>
                                <tr><td><strong>Memory:</strong></td><td>${instance.memory_gb ? instance.memory_gb + ' GB' : '-'}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Tags & Configuration</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Environment:</strong></td><td>${instance.env_tag || '-'}</td></tr>
                                <tr><td><strong>UAI:</strong></td><td>${instance.uai_tag || '-'}</td></tr>
                                <tr><td><strong>Patch Group:</strong></td><td>${instance.patch_group || '-'}</td></tr>
                                <tr><td><strong>CTO Managed:</strong></td><td>${instance.cto_cloud_ops_managed || '-'}</td></tr>
                                <tr><td><strong>Account:</strong></td><td>${instance.account_name}</td></tr>
                                <tr><td><strong>Region:</strong></td><td>${instance.region}</td></tr>
                                <tr><td><strong>Last Updated:</strong></td><td>${formatDate(instance.last_updated)}</td></tr>
                            </table>
                        </div>
                    </div>
                    ${instance.ssm_status ? `
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6>SSM Status</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getSSMColor(instance.ssm_status.ping_status)}">${instance.ssm_status.ping_status}</span></td></tr>
                                <tr><td><strong>Agent Version:</strong></td><td>${instance.ssm_status.agent_version || '-'}</td></tr>
                                <tr><td><strong>Latest Version:</strong></td><td>${instance.ssm_status.is_latest_version ? 'Yes' : 'No'}</td></tr>
                                <tr><td><strong>Last Ping:</strong></td><td>${formatDate(instance.ssm_status.last_ping_datetime)}</td></tr>
                            </table>
                        </div>
                    </div>
                    ` : ''}
                `;
                
                $('#instance-details').html(detailsHtml);
                $('#instanceModal').modal('show');
            },
            error: function(xhr) {
                showAlert('Failed to load instance details', 'danger');
            }
        });
    }

    function getStateColor(state) {
        switch(state) {
            case 'running': return 'success';
            case 'stopped': return 'danger';
            case 'pending': return 'warning';
            case 'stopping': return 'warning';
            case 'terminated': return 'dark';
            default: return 'secondary';
        }
    }

    function getSSMColor(status) {
        switch(status) {
            case 'Online': return 'success';
            case 'Connection Lost': return 'warning';
            case 'Inactive': return 'danger';
            default: return 'secondary';
        }
    }

    // Load data when page loads
    $(document).ready(function() {
        loadFilterOptions();
        loadInstances();
    });

    // Make loadData available globally for base template
    window.loadData = function() {
        loadInstances(currentPage);
    };
</script>
{% endblock %}
