{% extends 'inventory/base.html' %}

{% block title %}EC2 Instances - Cloud Operations Central{% endblock %}
{% block page_title %}EC2 Instances{% endblock %}

{% block page_actions %}
<div class="btn-group me-2">
    <button type="button" class="btn btn-outline-primary" onclick="exportToExcel()">
        <i class="fas fa-file-excel me-1"></i>
        Export Excel
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="loadInstances()">
        <i class="fas fa-sync-alt me-1"></i>
        Refresh
    </button>
</div>
<button type="button" class="btn btn-success" onclick="refreshAllInventory()">
    <i class="fas fa-sync-alt me-1"></i>
    Refresh All
</button>
{% endblock %}

{% block content %}
<!-- Compact Filters -->
<div class="filter-section compact-form">
    <div class="row g-2">
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="filter-account">
                <option value="">All Accounts</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="filter-state">
                <option value="">All States</option>
                <option value="running">Running</option>
                <option value="stopped">Stopped</option>
                <option value="pending">Pending</option>
                <option value="stopping">Stopping</option>
                <option value="terminated">Terminated</option>
            </select>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control form-control-sm" id="filter-env" placeholder="Environment">
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control form-control-sm" id="filter-uai" placeholder="UAI">
        </div>
        <div class="col-md-3">
            <input type="text" class="form-control form-control-sm" id="filter-search" placeholder="Search instances...">
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-primary btn-sm w-100" onclick="applyFilters()" title="Apply Filters">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    <div class="row g-2 mt-1">
        <div class="col-md-3">
            <select class="form-select form-select-sm" id="filter-tag-key">
                <option value="">Select Tag Key</option>
            </select>
        </div>
        <div class="col-md-3">
            <input type="text" class="form-control form-control-sm" id="filter-tag-value" placeholder="Tag value">
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control form-control-sm" id="filter-cto" placeholder="CTO Managed">
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>Clear
            </button>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-info btn-sm" onclick="loadAvailableTags()">
                <i class="fas fa-tags me-1"></i>Tags
            </button>
        </div>
    </div>
</div>

<!-- Compact Instances Table -->
<div class="card compact-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span class="fw-bold">EC2 Instances</span>
        <span class="badge bg-primary" id="instance-count">0 instances</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-compact table-striped table-hover mb-0" id="instances-table">
                <thead>
                    <tr>
                        <th style="width: 140px;">Instance ID</th>
                        <th style="width: 120px;">Name</th>
                        <th style="width: 80px;">Type</th>
                        <th style="width: 70px;">State</th>
                        <th style="width: 100px;">Private IP</th>
                        <th style="width: 100px;">Account</th>
                        <th style="width: 70px;">Env</th>
                        <th style="width: 80px;">UAI</th>
                        <th style="width: 80px;">CTO</th>
                        <th style="width: 80px;">SSM</th>
                        <th style="width: 60px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>

        <!-- Compact Pagination -->
        <div class="p-2 border-top bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted" id="pagination-info">Showing 0 of 0 instances</small>
                <nav aria-label="Instances pagination">
                    <ul class="pagination pagination-sm mb-0" id="pagination">
                        <!-- Pagination will be generated dynamically -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Compact Instance Detail Modal -->
<div class="modal fade" id="instanceModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h6 class="modal-title">Instance Details</h6>
                <button type="button" class="btn-close btn-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-3" id="instance-details">
                <!-- Instance details will be loaded here -->
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let currentFilters = {};

    function loadInstances(page = 1) {
        showLoading();
        
        let url = `/api/ec2-instances/?page=${page}`;
        
        // Add filters to URL
        Object.keys(currentFilters).forEach(key => {
            if (currentFilters[key]) {
                url += `&${key}=${encodeURIComponent(currentFilters[key])}`;
            }
        });
        
        $.ajax({
            url: url,
            method: 'GET',
            success: function(data) {
                updateInstancesTable(data.results);
                updatePagination(data);
                updateInstanceCount(data.count);
                hideLoading();
            },
            error: function(xhr) {
                hideLoading();
                showAlert('Failed to load instances', 'danger');
            }
        });
    }

    function updateInstancesTable(instances) {
        const tbody = $('#instances-table tbody');
        tbody.empty();

        if (instances && instances.length > 0) {
            instances.forEach(function(instance) {
                const row = `
                    <tr>
                        <td><code class="small">${instance.instance_id}</code></td>
                        <td class="text-truncate" style="max-width: 120px;" title="${instance.name || 'No name'}">${truncateText(instance.name || '-', 15)}</td>
                        <td><small>${instance.instance_type}</small></td>
                        <td><span class="badge badge-sm bg-${getStateColor(instance.state)}">${instance.state}</span></td>
                        <td><small>${instance.private_ip_address || '-'}</small></td>
                        <td class="text-truncate" style="max-width: 100px;" title="${instance.account_name}">${truncateText(instance.account_name, 12)}</td>
                        <td><small>${instance.env_tag || '-'}</small></td>
                        <td><small>${instance.uai_tag || '-'}</small></td>
                        <td><small>${truncateText(instance.cto_cloud_ops_managed || '-', 8)}</small></td>
                        <td><span class="badge badge-sm bg-${getSSMColor(instance.ssm_ping_status)}">${getSSMShortStatus(instance.ssm_ping_status)}</span></td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm" onclick="viewInstanceDetails(${instance.id})" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="11" class="text-center text-muted">No instances found</td></tr>');
        }
    }

    function truncateText(text, maxLength) {
        if (!text || text === '-') return text;
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    function getSSMShortStatus(status) {
        switch(status) {
            case 'Online': return 'ON';
            case 'Connection Lost': return 'LOST';
            case 'Inactive': return 'OFF';
            default: return 'UNK';
        }
    }

    function updatePagination(data) {
        const pagination = $('#pagination');
        const paginationInfo = $('#pagination-info');
        pagination.empty();

        const pageSize = 50;
        const startItem = ((currentPage - 1) * pageSize) + 1;
        const endItem = Math.min(currentPage * pageSize, data.count);

        paginationInfo.text(`Showing ${startItem}-${endItem} of ${data.count} instances`);

        if (data.count > pageSize) {
            const totalPages = Math.ceil(data.count / pageSize);

            // Previous button
            if (data.previous) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadInstances(${currentPage - 1})">&laquo;</a>
                    </li>
                `);
            }

            // Page numbers (show max 3 pages around current for compact view)
            const startPage = Math.max(1, currentPage - 1);
            const endPage = Math.min(totalPages, currentPage + 1);

            if (startPage > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadInstances(1)">1</a>
                    </li>
                `);
                if (startPage > 2) {
                    pagination.append(`<li class="page-item disabled"><span class="page-link page-link-sm">...</span></li>`);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link page-link-sm" href="#" onclick="loadInstances(${i})">${i}</a>
                    </li>
                `);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pagination.append(`<li class="page-item disabled"><span class="page-link page-link-sm">...</span></li>`);
                }
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadInstances(${totalPages})">${totalPages}</a>
                    </li>
                `);
            }

            // Next button
            if (data.next) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadInstances(${currentPage + 1})">&raquo;</a>
                    </li>
                `);
            }
        }
    }

    function updateInstanceCount(count) {
        $('#instance-count').text(`${count} instances`);
    }

    function loadFilterOptions() {
        // Load accounts for filter
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            success: function(data) {
                const accountSelect = $('#filter-account');
                data.results.forEach(function(account) {
                    accountSelect.append(`<option value="${account.account_id}">${account.account_name}</option>`);
                });
            }
        });
        
        // Load environments (this would need a separate endpoint or be extracted from instances)
        // For now, we'll populate common environments
        const envSelect = $('#filter-env');
        ['dev', 'test', 'staging', 'prod', 'production'].forEach(function(env) {
            envSelect.append(`<option value="${env}">${env}</option>`);
        });
    }

    function loadAvailableTags() {
        $.ajax({
            url: '/api/available-tags/',
            method: 'GET',
            success: function(data) {
                const tagKeySelect = $('#filter-tag-key');
                tagKeySelect.empty().append('<option value="">Select Tag Key</option>');

                data.tag_keys.forEach(function(key) {
                    tagKeySelect.append(`<option value="${key}">${key}</option>`);
                });

                showAlert(`Loaded ${data.total_unique_keys} unique tag keys`, 'success');
            },
            error: function(xhr) {
                showAlert('Failed to load available tags', 'warning');
            }
        });
    }

    function applyFilters() {
        currentFilters = {
            account_id: $('#filter-account').val(),
            state: $('#filter-state').val(),
            env_tag: $('#filter-env').val(),
            uai_tag: $('#filter-uai').val(),
            cto_cloud_ops_managed: $('#filter-cto').val(),
            tag_key: $('#filter-tag-key').val(),
            tag_value: $('#filter-tag-value').val(),
            search: $('#filter-search').val()
        };
        currentPage = 1;
        loadInstances(1);
    }

    function clearFilters() {
        $('#filter-account').val('');
        $('#filter-state').val('');
        $('#filter-env').val('');
        $('#filter-uai').val('');
        $('#filter-cto').val('');
        $('#filter-tag-key').val('');
        $('#filter-tag-value').val('');
        $('#filter-search').val('');
        currentFilters = {};
        currentPage = 1;
        loadInstances(1);
    }

    // Initialize autocomplete for search fields
    function initializeAutocompleteFields() {
        // Environment autocomplete
        const envSuggestions = ['prod', 'dev', 'test', 'staging', 'qa', 'uat'];
        initializeAutocomplete(document.getElementById('filter-env'), envSuggestions);

        // UAI autocomplete
        function fetchUAISuggestions(query, callback) {
            $.ajax({
                url: '/api/ec2-instances/',
                data: { search: 'UAI' + query, page_size: 10 },
                success: function(data) {
                    const uais = [...new Set(data.results
                        .map(instance => instance.uai_tag)
                        .filter(uai => uai && uai.toLowerCase().includes(query.toLowerCase()))
                    )];
                    callback(uais);
                }
            });
        }
        initializeAutocomplete(document.getElementById('filter-uai'), fetchUAISuggestions);

        // CTO Managed autocomplete
        const ctoSuggestions = ['Yes', 'No', 'NotAssigned'];
        initializeAutocomplete(document.getElementById('filter-cto'), ctoSuggestions);

        // Search field with debounced auto-search
        const searchInput = document.getElementById('filter-search');
        const debouncedSearch = debounce(function() {
            if (searchInput.value.length >= 2 || searchInput.value.length === 0) {
                applyFilters();
            }
        }, 500);

        searchInput.addEventListener('input', debouncedSearch);
    }

    function exportToExcel() {
        showLoading();
        
        let url = '/api/ec2-instances/export_excel/';
        
        // Add current filters to export
        const params = new URLSearchParams(currentFilters);
        if (params.toString()) {
            url += '?' + params.toString();
        }
        
        window.location.href = url;
        hideLoading();
    }

    function viewInstanceDetails(instanceId) {
        $.ajax({
            url: `/api/ec2-instances/${instanceId}/`,
            method: 'GET',
            success: function(instance) {
                const detailsHtml = `
                    <div class="row g-2">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header py-1"><small class="fw-bold">Basic Information</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 40%;">Instance ID:</td><td><code>${instance.instance_id}</code></td></tr>
                                        <tr><td class="fw-bold">Name:</td><td>${instance.name || '-'}</td></tr>
                                        <tr><td class="fw-bold">Type:</td><td>${instance.instance_type}</td></tr>
                                        <tr><td class="fw-bold">State:</td><td><span class="badge bg-${getStateColor(instance.state)}">${instance.state}</span></td></tr>
                                        <tr><td class="fw-bold">vCPU:</td><td>${instance.vcpu_count || '-'}</td></tr>
                                        <tr><td class="fw-bold">Memory:</td><td>${instance.memory_gb ? instance.memory_gb + ' GB' : '-'}</td></tr>
                                        <tr><td class="fw-bold">AMI:</td><td><code class="small">${instance.ami_id || '-'}</code></td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header py-1"><small class="fw-bold">Network & Security</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 40%;">Private IP:</td><td>${instance.private_ip_address || '-'}</td></tr>
                                        <tr><td class="fw-bold">Subnet ID:</td><td><code class="small">${instance.subnet_id || '-'}</code></td></tr>
                                        <tr><td class="fw-bold">Subnet Name:</td><td>${instance.subnet_name || '-'}</td></tr>
                                        <tr><td class="fw-bold">Instance Profile:</td><td class="small">${truncateText(instance.instance_profile || '-', 20)}</td></tr>
                                        <tr><td class="fw-bold">Security Groups:</td><td class="small">${getSecurityGroupsDisplay(instance.security_groups_list)}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header py-1"><small class="fw-bold">Account & Storage</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 40%;">Business Unit:</td><td>${instance.business_unit || '-'}</td></tr>
                                        <tr><td class="fw-bold">Account:</td><td>${instance.account_name}</td></tr>
                                        <tr><td class="fw-bold">Region:</td><td>${instance.region}</td></tr>
                                        <tr><td class="fw-bold">Root Volume:</td><td><code class="small">${instance.root_volume_id || '-'}</code></td></tr>
                                        <tr><td class="fw-bold">Root Size:</td><td>${instance.root_volume_size_gb ? instance.root_volume_size_gb + ' GB' : '-'}</td></tr>
                                        <tr><td class="fw-bold">Data Disks:</td><td>${instance.data_disk_count || 0}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-2 mt-1">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">Tags</small></div>
                                <div class="card-body p-2">
                                    <div class="row g-1" style="font-size: 0.75rem;">
                                        <div class="col-6"><span class="fw-bold">Environment:</span> ${instance.env_tag || '-'}</div>
                                        <div class="col-6"><span class="fw-bold">UAI:</span> ${instance.uai_tag || '-'}</div>
                                        <div class="col-6"><span class="fw-bold">CTO Managed:</span> ${instance.cto_cloud_ops_managed || '-'}</div>
                                        <div class="col-6"><span class="fw-bold">Patch Group:</span> ${instance.patch_group || '-'}</div>
                                        <div class="col-6"><span class="fw-bold">Backup:</span> ${instance.backup_tag || '-'}</div>
                                        <div class="col-6"><span class="fw-bold">Schedule:</span> ${instance.schedule_tag || '-'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            ${instance.ssm_status ? `
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">SSM Status</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 40%;">Status:</td><td><span class="badge bg-${getSSMColor(instance.ssm_status.ping_status)}">${instance.ssm_status.ping_status}</span></td></tr>
                                        <tr><td class="fw-bold">Agent Version:</td><td>${instance.ssm_status.agent_version || '-'}</td></tr>
                                        <tr><td class="fw-bold">Latest Version:</td><td>${instance.ssm_status.is_latest_version ? 'Yes' : 'No'}</td></tr>
                                        <tr><td class="fw-bold">Last Ping:</td><td class="small">${formatDate(instance.ssm_status.last_ping_datetime)}</td></tr>
                                        <tr><td class="fw-bold">Platform:</td><td>${instance.ssm_status.platform_name || '-'}</td></tr>
                                    </table>
                                </div>
                            </div>
                            ` : `
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">SSM Status</small></div>
                                <div class="card-body p-2 text-center text-muted">
                                    <small>No SSM data available</small>
                                </div>
                            </div>
                            `}
                        </div>
                    </div>

                    ${instance.instance_tags && instance.instance_tags.length > 0 ? `
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">All Tags (${instance.instance_tags.length})</small></div>
                                <div class="card-body p-2">
                                    <div class="d-flex flex-wrap gap-1">
                                        ${instance.instance_tags.map(tag => `
                                            <span class="badge bg-light text-dark border" style="font-size: 0.7rem;">
                                                <strong>${tag.key}</strong>=<span class="text-muted">${tag.value}</span>
                                            </span>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                `;
                
                $('#instance-details').html(detailsHtml);
                $('#instanceModal').modal('show');
            },
            error: function(xhr) {
                showAlert('Failed to load instance details', 'danger');
            }
        });
    }

    function getStateColor(state) {
        switch(state) {
            case 'running': return 'success';
            case 'stopped': return 'danger';
            case 'pending': return 'warning';
            case 'stopping': return 'warning';
            case 'terminated': return 'dark';
            default: return 'secondary';
        }
    }

    function getSSMColor(status) {
        switch(status) {
            case 'Online': return 'success';
            case 'Connection Lost': return 'warning';
            case 'Inactive': return 'danger';
            default: return 'secondary';
        }
    }

    function getSecurityGroupsDisplay(securityGroups) {
        if (!securityGroups || securityGroups.length === 0) {
            return '-';
        }

        return securityGroups.map(sg => `${sg.name} (${sg.id})`).join('<br>');
    }

    // Load data when page loads
    $(document).ready(function() {
        loadFilterOptions();
        loadAvailableTags();
        initializeAutocompleteFields();
        loadInstances();
    });

    // Make loadData available globally for base template
    window.loadData = function() {
        loadInstances(currentPage);
    };
</script>
{% endblock %}
