{% extends 'inventory/base.html' %}

{% block title %}Dashboard - Cloud Operations Central{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block page_actions %}
<!-- No refresh button on dashboard -->
{% endblock %}

{% block page_header %}
<!-- No page header on dashboard -->
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard-specific styles for non-scrollable layout */
    .main-content {
        height: calc(100vh - var(--header-height) - var(--footer-height));
        overflow: hidden;
        padding: 12px 8px 8px 8px; /* Top padding to account for removed page header */
        margin-top: 0 !important;
    }

    /* Hide only the page header div, not section headers */
    .main-content .page-header {
        display: none !important;
    }

    /* Ensure dashboard container starts with proper spacing */
    .dashboard-container {
        padding-top: 40px; /* Additional top padding to prevent header hiding */
    }

    /* Popup notification styles */
    .notification-popup {
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 9999;
        max-width: 350px;
        min-width: 300px;
        padding: 12px 16px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(400px);
        transition: transform 0.3s ease-in-out;
        font-size: 0.9rem;
    }

    .notification-popup.show {
        transform: translateX(0);
    }

    .notification-popup.alert-success {
        background-color: #d1edff;
        border-left: 4px solid #0d6efd;
        color: #0c5460;
    }

    .notification-popup.alert-danger {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
        color: #721c24;
    }

    .notification-popup.alert-warning {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        color: #856404;
    }

    .notification-popup.alert-info {
        background-color: #d1ecf1;
        border-left: 4px solid #17a2b8;
        color: #0c5460;
    }

    .notification-popup .close-btn {
        position: absolute;
        top: 8px;
        right: 12px;
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        color: inherit;
        opacity: 0.7;
    }

    .notification-popup .close-btn:hover {
        opacity: 1;
    }

    .dashboard-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .cloud-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
    }

    .aws-section {
        flex: 1.2; /* Slightly more space for AWS */
    }

    .azure-section {
        flex: 1;
    }

    .section-header {
        padding: 6px 12px;
        border-radius: 4px;
        color: white;
        font-size: 0.9rem;
        margin-bottom: 6px;
        text-align: center;
    }

    .aws-header {
        background: linear-gradient(135deg, #FF9900 0%, #FF6600 100%);
    }

    .azure-header {
        background: linear-gradient(135deg, #0078D4 0%, #005A9E 100%);
    }

    .tables-row {
        display: flex;
        gap: 8px;
        flex: 1;
        min-height: 0;
    }

    .table-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        min-height: 0;
    }

    .table-header {
        padding: 6px 10px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 6px 6px 0 0;
        font-size: 0.8rem;
        text-align: center;
    }

    .table-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
    }

    .table-content table {
        height: 100%;
    }

    .table-compact {
        font-size: 0.75rem;
        margin: 0;
    }

    .table-compact th,
    .table-compact td {
        padding: 0.3rem 0.5rem;
        vertical-align: middle;
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .table-compact th {
        background-color: #f8f9fa;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table-compact .badge {
        font-size: 0.65rem;
        padding: 0.2em 0.4em;
    }

    .quick-actions {
        height: 50px;
        margin-top: 8px;
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        height: 100%;
    }

    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        border-radius: 4px;
        padding: 6px;
        font-size: 0.75rem;
        transition: all 0.2s;
        border: 1px solid #dee2e6;
    }

    .action-btn.btn-primary {
        background-color: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }

    .action-btn.btn-outline {
        background-color: white;
        color: #6c757d;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-decoration: none;
    }

    .action-btn.btn-primary:hover {
        background-color: #0b5ed7;
        color: white;
    }

    .action-btn.btn-outline:hover {
        background-color: #f8f9fa;
        color: #495057;
    }

    .action-btn i {
        font-size: 1rem;
        margin-bottom: 2px;
    }

    .action-btn span {
        font-size: 0.7rem;
        font-weight: 500;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .tables-row {
            flex-direction: column;
            gap: 6px;
        }

        .table-container {
            min-height: 120px;
        }

        .actions-grid {
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
        }

        .quick-actions {
            height: 70px;
        }
    }

    @media (max-width: 576px) {
        .main-content {
            padding: 4px;
        }

        .dashboard-container {
            gap: 4px;
        }

        .tables-row {
            gap: 4px;
        }

        .table-compact {
            font-size: 0.7rem;
        }

        .table-compact th,
        .table-compact td {
            padding: 0.2rem 0.3rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- AWS Section -->
    <div class="cloud-section aws-section">
        <div class="section-header aws-header">
            <i class="fab fa-aws fa-lg me-2"></i>
            <span class="fw-bold">AWS</span>
        </div>

        <div class="tables-row">
            <!-- AWS Accounts -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">Accounts</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 60%;">BU</th>
                                <th style="width: 40%;">Count</th>
                            </tr>
                        </thead>
                        <tbody id="aws-accounts-table">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- AWS EC2 Instances -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">EC2 Instances</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Running</th>
                                <th style="width: 20%;">SSM Online</th>
                            </tr>
                        </thead>
                        <tbody id="aws-ec2-table">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- AWS EKS Clusters -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">EKS Clusters</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 25%;">BU</th>
                                <th style="width: 25%;">Total</th>
                                <th style="width: 25%;">EC2</th>
                                <th style="width: 25%;">Fargate</th>
                            </tr>
                        </thead>
                        <tbody id="aws-eks-table">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Azure Section -->
    <div class="cloud-section azure-section">
        <div class="section-header azure-header">
            <i class="fab fa-microsoft fa-lg me-2"></i>
            <span class="fw-bold">Azure</span>
        </div>

        <div class="tables-row">
            <!-- Azure Subscriptions -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">Subscriptions</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 60%;">BU</th>
                                <th style="width: 40%;">Count</th>
                            </tr>
                        </thead>
                        <tbody id="azure-subscriptions-table">
                            <tr><td colspan="2" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Azure Virtual Machines -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">Virtual Machines</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Running</th>
                                <th style="width: 20%;">Agent</th>
                            </tr>
                        </thead>
                        <tbody id="azure-vm-table">
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Azure AKS Clusters -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">AKS Clusters</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Public</th>
                                <th style="width: 20%;">Private</th>
                            </tr>
                        </thead>
                        <tbody id="azure-aks-table">
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="quick-actions">
        <div class="actions-grid">
            <a href="{% url 'inventory:ec2_instances' %}" class="action-btn btn-primary">
                <i class="fas fa-server"></i>
                <span>EC2 Instances</span>
            </a>
            <a href="{% url 'inventory:ssm_status' %}" class="action-btn btn-outline">
                <i class="fas fa-heartbeat"></i>
                <span>SSM Status</span>
            </a>
            <a href="{% url 'inventory:accounts' %}" class="action-btn btn-outline">
                <i class="fas fa-users"></i>
                <span>AWS Accounts</span>
            </a>
            <a href="#" onclick="showPlaceholder('Azure Management')" class="action-btn btn-outline">
                <i class="fab fa-microsoft"></i>
                <span>Azure Portal</span>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function loadDashboardData() {
        showLoading();

        // Load AWS dashboard data
        loadAWSData();

        hideLoading();
    }

    function loadAWSData() {
        // Load AWS accounts by BU
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            data: { page_size: 1000 },
            success: function(data) {
                updateAWSAccountsTable(data.results);
                showPopupNotification('AWS accounts data loaded successfully', 'success');
            },
            error: function(xhr) {
                showPopupNotification('Failed to load AWS accounts data', 'danger');
            }
        });

        // Load AWS EC2 instances by BU
        $.ajax({
            url: '/api/ec2-instances/',
            method: 'GET',
            data: { page_size: 1000 },
            success: function(data) {
                updateAWSEC2Table(data.results);
                showPopupNotification('AWS EC2 data loaded successfully', 'success');
            },
            error: function(xhr) {
                showPopupNotification('Failed to load AWS EC2 data', 'danger');
            }
        });

        // Load AWS EKS clusters by BU
        $.ajax({
            url: '/api/eks-clusters/',
            method: 'GET',
            data: { page_size: 1000 },
            success: function(data) {
                updateAWSEKSTable(data.results);
                showPopupNotification('AWS EKS data loaded successfully', 'success');
            },
            error: function(xhr) {
                showPopupNotification('Failed to load AWS EKS data', 'danger');
            }
        });
    }

    function updateAWSAccountsTable(accounts) {
        const tbody = $('#aws-accounts-table');
        tbody.empty();

        // Group accounts by business unit
        const buGroups = {};
        accounts.forEach(account => {
            const bu = account.business_unit;
            if (!buGroups[bu]) {
                buGroups[bu] = 0;
            }
            buGroups[bu]++;
        });

        // Add rows for each BU
        Object.keys(buGroups).sort().forEach(bu => {
            const row = `
                <tr>
                    <td><small class="fw-bold">${bu}</small></td>
                    <td><span class="badge bg-primary">${buGroups[bu]}</span></td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add total row
        const totalRow = `
            <tr class="table-active">
                <td><small class="fw-bold">Total</small></td>
                <td><span class="badge bg-success">${accounts.length}</span></td>
            </tr>
        `;
        tbody.append(totalRow);
    }

    function updateAWSEC2Table(instances) {
        const tbody = $('#aws-ec2-table');
        tbody.empty();

        // Group instances by business unit and calculate stats
        const buStats = {};

        instances.forEach(instance => {
            const bu = instance.business_unit || 'Unknown';
            if (!buStats[bu]) {
                buStats[bu] = {
                    total: 0,
                    running: 0,
                    ssmOnline: 0
                };
            }

            buStats[bu].total++;

            if (instance.state === 'running') {
                buStats[bu].running++;
            }

            if (instance.ssm_ping_status === 'Online') {
                buStats[bu].ssmOnline++;
            }
        });

        // Add rows for each BU
        Object.keys(buStats).sort().forEach(bu => {
            const stats = buStats[bu];
            const row = `
                <tr>
                    <td><small class="fw-bold">${bu}</small></td>
                    <td><span class="badge bg-info">${stats.total}</span></td>
                    <td><span class="badge bg-success">${stats.running}</span></td>
                    <td><span class="badge bg-primary">${stats.ssmOnline}</span></td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add total row
        const totalInstances = instances.length;
        const totalRunning = instances.filter(i => i.state === 'running').length;
        const totalSSMOnline = instances.filter(i => i.ssm_ping_status === 'Online').length;

        const totalRow = `
            <tr class="table-active">
                <td><small class="fw-bold">Total</small></td>
                <td><span class="badge bg-info">${totalInstances}</span></td>
                <td><span class="badge bg-success">${totalRunning}</span></td>
                <td><span class="badge bg-primary">${totalSSMOnline}</span></td>
            </tr>
        `;
        tbody.append(totalRow);
    }

    function updateAWSEKSTable(clusters) {
        const tbody = $('#aws-eks-table');
        tbody.empty();

        // Group clusters by business unit and calculate stats
        const buStats = {};

        clusters.forEach(cluster => {
            const bu = cluster.business_unit || 'Unknown';
            if (!buStats[bu]) {
                buStats[bu] = {
                    total: 0,
                    ec2Nodes: 0,
                    fargateProfiles: 0
                };
            }

            buStats[bu].total++;
            buStats[bu].ec2Nodes += cluster.node_groups_count || 0;
            buStats[bu].fargateProfiles += cluster.fargate_profiles_count || 0;
        });

        // Add rows for each BU
        Object.keys(buStats).sort().forEach(bu => {
            const stats = buStats[bu];
            const row = `
                <tr>
                    <td><small class="fw-bold">${bu}</small></td>
                    <td><span class="badge bg-info">${stats.total}</span></td>
                    <td><span class="badge bg-success">${stats.ec2Nodes}</span></td>
                    <td><span class="badge bg-secondary">${stats.fargateProfiles}</span></td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add total row
        const totalClusters = clusters.length;
        const totalEC2Nodes = clusters.reduce((sum, c) => sum + (c.node_groups_count || 0), 0);
        const totalFargate = clusters.reduce((sum, c) => sum + (c.fargate_profiles_count || 0), 0);

        const totalRow = `
            <tr class="table-active">
                <td><small class="fw-bold">Total</small></td>
                <td><span class="badge bg-info">${totalClusters}</span></td>
                <td><span class="badge bg-success">${totalEC2Nodes}</span></td>
                <td><span class="badge bg-secondary">${totalFargate}</span></td>
            </tr>
        `;
        tbody.append(totalRow);
    }

    // Popup notification function
    function showPopupNotification(message, type = 'info') {
        // Remove existing notifications
        $('.notification-popup').remove();

        // Create notification element
        const notification = $(`
            <div class="notification-popup alert-${type}">
                <button class="close-btn" onclick="$(this).parent().removeClass('show')">&times;</button>
                <div>${message}</div>
            </div>
        `);

        // Add to body
        $('body').append(notification);

        // Show with animation
        setTimeout(() => notification.addClass('show'), 100);

        // Auto-hide after 4 seconds
        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }

    // Override global showAlert function for popup style
    window.showAlert = function(message, type = 'info') {
        showPopupNotification(message, type);
    };

    // Load data when page loads
    $(document).ready(function() {
        loadDashboardData();

        // Auto-refresh every 5 minutes
        setInterval(loadDashboardData, 300000);
    });

    // Make loadData available globally for base template
    window.loadData = loadDashboardData;
</script>
{% endblock %}
