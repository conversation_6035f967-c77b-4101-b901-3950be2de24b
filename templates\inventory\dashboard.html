{% extends 'inventory/base.html' %}

{% block title %}Dashboard - Cloud Operations Central{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block page_actions %}
<!-- No refresh button on dashboard -->
{% endblock %}

{% block content %}
<!-- AWS Section -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card border-0" style="background: linear-gradient(135deg, #FF9900 0%, #FF6600 100%); color: white;">
            <div class="card-body py-2">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fab fa-aws fa-2x me-3"></i>
                    <h4 class="mb-0">AWS</h4>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AWS Data Tables -->
<div class="row mb-4">
    <!-- AWS Accounts -->
    <div class="col-md-4 mb-3">
        <div class="card compact-card">
            <div class="card-header py-2">
                <small class="fw-bold">Accounts</small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 60%;">BU</th>
                                <th style="width: 40%;">Accounts</th>
                            </tr>
                        </thead>
                        <tbody id="aws-accounts-table">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- AWS EC2 Instances -->
    <div class="col-md-4 mb-3">
        <div class="card compact-card">
            <div class="card-header py-2">
                <small class="fw-bold">EC2 Instances</small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Running</th>
                                <th style="width: 20%;">SSM Online</th>
                            </tr>
                        </thead>
                        <tbody id="aws-ec2-table">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- AWS EKS Clusters -->
    <div class="col-md-4 mb-3">
        <div class="card compact-card">
            <div class="card-header py-2">
                <small class="fw-bold">EKS Clusters</small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 25%;">BU</th>
                                <th style="width: 25%;">Total</th>
                                <th style="width: 25%;">EC2 Nodes</th>
                                <th style="width: 25%;">Fargate</th>
                            </tr>
                        </thead>
                        <tbody id="aws-eks-table">
                            <!-- Placeholder data -->
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Azure Section -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card border-0" style="background: linear-gradient(135deg, #0078D4 0%, #005A9E 100%); color: white;">
            <div class="card-body py-2">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fab fa-microsoft fa-2x me-3"></i>
                    <h4 class="mb-0">Azure</h4>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Azure Data Tables -->
<div class="row mb-4">
    <!-- Azure Subscriptions -->
    <div class="col-md-4 mb-3">
        <div class="card compact-card">
            <div class="card-header py-2">
                <small class="fw-bold">Subscriptions</small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 60%;">BU</th>
                                <th style="width: 40%;">Subscriptions</th>
                            </tr>
                        </thead>
                        <tbody id="azure-subscriptions-table">
                            <!-- Placeholder data -->
                            <tr><td colspan="2" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Azure Virtual Machines -->
    <div class="col-md-4 mb-3">
        <div class="card compact-card">
            <div class="card-header py-2">
                <small class="fw-bold">Virtual Machines</small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Running</th>
                                <th style="width: 20%;">VM Agent</th>
                            </tr>
                        </thead>
                        <tbody id="azure-vm-table">
                            <!-- Placeholder data -->
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Azure AKS Clusters -->
    <div class="col-md-4 mb-3">
        <div class="card compact-card">
            <div class="card-header py-2">
                <small class="fw-bold">AKS Clusters</small>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Public</th>
                                <th style="width: 20%;">Private</th>
                            </tr>
                        </thead>
                        <tbody id="azure-aks-table">
                            <!-- Placeholder data -->
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="row">
    <div class="col-12">
        <div class="card compact-card">
            <div class="card-header py-2">
                <small class="fw-bold">Quick Actions</small>
            </div>
            <div class="card-body p-2">
                <div class="row g-2">
                    <div class="col-md-3">
                        <a href="{% url 'inventory:ec2_instances' %}" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-server me-1"></i>EC2 Instances
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'inventory:ssm_status' %}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-heartbeat me-1"></i>SSM Status
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'inventory:accounts' %}" class="btn btn-outline-secondary btn-sm w-100">
                            <i class="fas fa-users me-1"></i>AWS Accounts
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="#" onclick="showPlaceholder('Azure Management')" class="btn btn-outline-info btn-sm w-100">
                            <i class="fab fa-microsoft me-1"></i>Azure Portal
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function loadDashboardData() {
        showLoading();

        // Load AWS dashboard data
        loadAWSData();

        hideLoading();
    }

    function loadAWSData() {
        // Load AWS accounts by BU
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            data: { page_size: 1000 },
            success: function(data) {
                updateAWSAccountsTable(data.results);
            },
            error: function(xhr) {
                showAlert('Failed to load AWS accounts data', 'warning');
            }
        });

        // Load AWS EC2 instances by BU
        $.ajax({
            url: '/api/ec2-instances/',
            method: 'GET',
            data: { page_size: 1000 },
            success: function(data) {
                updateAWSEC2Table(data.results);
            },
            error: function(xhr) {
                showAlert('Failed to load AWS EC2 data', 'warning');
            }
        });
    }

    function updateAWSAccountsTable(accounts) {
        const tbody = $('#aws-accounts-table');
        tbody.empty();

        // Group accounts by business unit
        const buGroups = {};
        accounts.forEach(account => {
            const bu = account.business_unit;
            if (!buGroups[bu]) {
                buGroups[bu] = 0;
            }
            buGroups[bu]++;
        });

        // Add rows for each BU
        Object.keys(buGroups).sort().forEach(bu => {
            const row = `
                <tr>
                    <td><small class="fw-bold">${bu}</small></td>
                    <td><span class="badge bg-primary">${buGroups[bu]}</span></td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add total row
        const totalRow = `
            <tr class="table-active">
                <td><small class="fw-bold">Total</small></td>
                <td><span class="badge bg-success">${accounts.length}</span></td>
            </tr>
        `;
        tbody.append(totalRow);
    }

    function updateAWSEC2Table(instances) {
        const tbody = $('#aws-ec2-table');
        tbody.empty();

        // Group instances by business unit and calculate stats
        const buStats = {};

        instances.forEach(instance => {
            const bu = instance.business_unit || 'Unknown';
            if (!buStats[bu]) {
                buStats[bu] = {
                    total: 0,
                    running: 0,
                    ssmOnline: 0
                };
            }

            buStats[bu].total++;

            if (instance.state === 'running') {
                buStats[bu].running++;
            }

            if (instance.ssm_ping_status === 'Online') {
                buStats[bu].ssmOnline++;
            }
        });

        // Add rows for each BU
        Object.keys(buStats).sort().forEach(bu => {
            const stats = buStats[bu];
            const row = `
                <tr>
                    <td><small class="fw-bold">${bu}</small></td>
                    <td><span class="badge bg-info">${stats.total}</span></td>
                    <td><span class="badge bg-success">${stats.running}</span></td>
                    <td><span class="badge bg-primary">${stats.ssmOnline}</span></td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add total row
        const totalInstances = instances.length;
        const totalRunning = instances.filter(i => i.state === 'running').length;
        const totalSSMOnline = instances.filter(i => i.ssm_ping_status === 'Online').length;

        const totalRow = `
            <tr class="table-active">
                <td><small class="fw-bold">Total</small></td>
                <td><span class="badge bg-info">${totalInstances}</span></td>
                <td><span class="badge bg-success">${totalRunning}</span></td>
                <td><span class="badge bg-primary">${totalSSMOnline}</span></td>
            </tr>
        `;
        tbody.append(totalRow);
    }

    // Load data when page loads
    $(document).ready(function() {
        loadDashboardData();

        // Auto-refresh every 5 minutes
        setInterval(loadDashboardData, 300000);
    });

    // Make loadData available globally for base template
    window.loadData = loadDashboardData;
</script>
{% endblock %}
