{% extends 'inventory/base.html' %}

{% block title %}Dashboard - Cloud Operations Central{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block page_actions %}
<!-- No refresh button on dashboard -->
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard-specific styles for non-scrollable layout */
    .main-content {
        height: calc(100vh - var(--header-height) - var(--footer-height));
        overflow: hidden;
        padding: 8px;
    }

    .dashboard-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .cloud-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
    }

    .aws-section {
        flex: 1.2; /* Slightly more space for AWS */
    }

    .azure-section {
        flex: 1;
    }

    .section-header {
        padding: 6px 12px;
        border-radius: 4px;
        color: white;
        font-size: 0.9rem;
        margin-bottom: 6px;
        text-align: center;
    }

    .aws-header {
        background: linear-gradient(135deg, #FF9900 0%, #FF6600 100%);
    }

    .azure-header {
        background: linear-gradient(135deg, #0078D4 0%, #005A9E 100%);
    }

    .tables-row {
        display: flex;
        gap: 8px;
        flex: 1;
        min-height: 0;
    }

    .table-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        min-height: 0;
    }

    .table-header {
        padding: 6px 10px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 6px 6px 0 0;
        font-size: 0.8rem;
        text-align: center;
    }

    .table-content {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
    }

    .table-content table {
        height: 100%;
    }

    .table-compact {
        font-size: 0.75rem;
        margin: 0;
    }

    .table-compact th,
    .table-compact td {
        padding: 0.3rem 0.5rem;
        vertical-align: middle;
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .table-compact th {
        background-color: #f8f9fa;
        font-weight: 600;
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table-compact .badge {
        font-size: 0.65rem;
        padding: 0.2em 0.4em;
    }

    .quick-actions {
        height: 50px;
        margin-top: 8px;
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        height: 100%;
    }

    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        border-radius: 4px;
        padding: 6px;
        font-size: 0.75rem;
        transition: all 0.2s;
        border: 1px solid #dee2e6;
    }

    .action-btn.btn-primary {
        background-color: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }

    .action-btn.btn-outline {
        background-color: white;
        color: #6c757d;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-decoration: none;
    }

    .action-btn.btn-primary:hover {
        background-color: #0b5ed7;
        color: white;
    }

    .action-btn.btn-outline:hover {
        background-color: #f8f9fa;
        color: #495057;
    }

    .action-btn i {
        font-size: 1rem;
        margin-bottom: 2px;
    }

    .action-btn span {
        font-size: 0.7rem;
        font-weight: 500;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .tables-row {
            flex-direction: column;
            gap: 6px;
        }

        .table-container {
            min-height: 120px;
        }

        .actions-grid {
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
        }

        .quick-actions {
            height: 70px;
        }
    }

    @media (max-width: 576px) {
        .main-content {
            padding: 4px;
        }

        .dashboard-container {
            gap: 4px;
        }

        .tables-row {
            gap: 4px;
        }

        .table-compact {
            font-size: 0.7rem;
        }

        .table-compact th,
        .table-compact td {
            padding: 0.2rem 0.3rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- AWS Section -->
    <div class="cloud-section aws-section">
        <div class="section-header aws-header">
            <i class="fab fa-aws fa-lg me-2"></i>
            <span class="fw-bold">AWS</span>
        </div>

        <div class="tables-row">
            <!-- AWS Accounts -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">Accounts</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 60%;">BU</th>
                                <th style="width: 40%;">Count</th>
                            </tr>
                        </thead>
                        <tbody id="aws-accounts-table">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- AWS EC2 Instances -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">EC2 Instances</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Running</th>
                                <th style="width: 20%;">SSM Online</th>
                            </tr>
                        </thead>
                        <tbody id="aws-ec2-table">
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- AWS EKS Clusters -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">EKS Clusters</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 25%;">BU</th>
                                <th style="width: 25%;">Total</th>
                                <th style="width: 25%;">EC2</th>
                                <th style="width: 25%;">Fargate</th>
                            </tr>
                        </thead>
                        <tbody id="aws-eks-table">
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Azure Section -->
    <div class="cloud-section azure-section">
        <div class="section-header azure-header">
            <i class="fab fa-microsoft fa-lg me-2"></i>
            <span class="fw-bold">Azure</span>
        </div>

        <div class="tables-row">
            <!-- Azure Subscriptions -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">Subscriptions</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 60%;">BU</th>
                                <th style="width: 40%;">Count</th>
                            </tr>
                        </thead>
                        <tbody id="azure-subscriptions-table">
                            <tr><td colspan="2" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Azure Virtual Machines -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">Virtual Machines</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Running</th>
                                <th style="width: 20%;">Agent</th>
                            </tr>
                        </thead>
                        <tbody id="azure-vm-table">
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Azure AKS Clusters -->
            <div class="table-container">
                <div class="table-header">
                    <small class="fw-bold">AKS Clusters</small>
                </div>
                <div class="table-content">
                    <table class="table table-compact table-striped mb-0">
                        <thead>
                            <tr>
                                <th style="width: 40%;">BU</th>
                                <th style="width: 20%;">Total</th>
                                <th style="width: 20%;">Public</th>
                                <th style="width: 20%;">Private</th>
                            </tr>
                        </thead>
                        <tbody id="azure-aks-table">
                            <tr><td colspan="4" class="text-center text-muted small">Coming Soon</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="quick-actions">
        <div class="actions-grid">
            <a href="{% url 'inventory:ec2_instances' %}" class="action-btn btn-primary">
                <i class="fas fa-server"></i>
                <span>EC2 Instances</span>
            </a>
            <a href="{% url 'inventory:ssm_status' %}" class="action-btn btn-outline">
                <i class="fas fa-heartbeat"></i>
                <span>SSM Status</span>
            </a>
            <a href="{% url 'inventory:accounts' %}" class="action-btn btn-outline">
                <i class="fas fa-users"></i>
                <span>AWS Accounts</span>
            </a>
            <a href="#" onclick="showPlaceholder('Azure Management')" class="action-btn btn-outline">
                <i class="fab fa-microsoft"></i>
                <span>Azure Portal</span>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function loadDashboardData() {
        showLoading();

        // Load AWS dashboard data
        loadAWSData();

        hideLoading();
    }

    function loadAWSData() {
        // Load AWS accounts by BU
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            data: { page_size: 1000 },
            success: function(data) {
                updateAWSAccountsTable(data.results);
            },
            error: function(xhr) {
                showAlert('Failed to load AWS accounts data', 'warning');
            }
        });

        // Load AWS EC2 instances by BU
        $.ajax({
            url: '/api/ec2-instances/',
            method: 'GET',
            data: { page_size: 1000 },
            success: function(data) {
                updateAWSEC2Table(data.results);
            },
            error: function(xhr) {
                showAlert('Failed to load AWS EC2 data', 'warning');
            }
        });
    }

    function updateAWSAccountsTable(accounts) {
        const tbody = $('#aws-accounts-table');
        tbody.empty();

        // Group accounts by business unit
        const buGroups = {};
        accounts.forEach(account => {
            const bu = account.business_unit;
            if (!buGroups[bu]) {
                buGroups[bu] = 0;
            }
            buGroups[bu]++;
        });

        // Add rows for each BU
        Object.keys(buGroups).sort().forEach(bu => {
            const row = `
                <tr>
                    <td><small class="fw-bold">${bu}</small></td>
                    <td><span class="badge bg-primary">${buGroups[bu]}</span></td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add total row
        const totalRow = `
            <tr class="table-active">
                <td><small class="fw-bold">Total</small></td>
                <td><span class="badge bg-success">${accounts.length}</span></td>
            </tr>
        `;
        tbody.append(totalRow);
    }

    function updateAWSEC2Table(instances) {
        const tbody = $('#aws-ec2-table');
        tbody.empty();

        // Group instances by business unit and calculate stats
        const buStats = {};

        instances.forEach(instance => {
            const bu = instance.business_unit || 'Unknown';
            if (!buStats[bu]) {
                buStats[bu] = {
                    total: 0,
                    running: 0,
                    ssmOnline: 0
                };
            }

            buStats[bu].total++;

            if (instance.state === 'running') {
                buStats[bu].running++;
            }

            if (instance.ssm_ping_status === 'Online') {
                buStats[bu].ssmOnline++;
            }
        });

        // Add rows for each BU
        Object.keys(buStats).sort().forEach(bu => {
            const stats = buStats[bu];
            const row = `
                <tr>
                    <td><small class="fw-bold">${bu}</small></td>
                    <td><span class="badge bg-info">${stats.total}</span></td>
                    <td><span class="badge bg-success">${stats.running}</span></td>
                    <td><span class="badge bg-primary">${stats.ssmOnline}</span></td>
                </tr>
            `;
            tbody.append(row);
        });

        // Add total row
        const totalInstances = instances.length;
        const totalRunning = instances.filter(i => i.state === 'running').length;
        const totalSSMOnline = instances.filter(i => i.ssm_ping_status === 'Online').length;

        const totalRow = `
            <tr class="table-active">
                <td><small class="fw-bold">Total</small></td>
                <td><span class="badge bg-info">${totalInstances}</span></td>
                <td><span class="badge bg-success">${totalRunning}</span></td>
                <td><span class="badge bg-primary">${totalSSMOnline}</span></td>
            </tr>
        `;
        tbody.append(totalRow);
    }

    // Load data when page loads
    $(document).ready(function() {
        loadDashboardData();

        // Auto-refresh every 5 minutes
        setInterval(loadDashboardData, 300000);
    });

    // Make loadData available globally for base template
    window.loadData = loadDashboardData;
</script>
{% endblock %}
