{% extends 'inventory/base.html' %}

{% block title %}Dashboard - Cloud Operations Central{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<!-- Compact Welcome Section -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body text-center py-3">
                <h4 class="mb-2">Welcome to Cloud Operations Central</h4>
                <p class="mb-0 small">Your comprehensive platform for cloud infrastructure management and automation</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Statistics Cards -->
    <div class="col-md-3 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">Total Accounts</h6>
                        <h3 class="mb-0 text-primary" id="total-accounts">-</h3>
                        <small class="text-muted">AWS Accounts</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">Total Instances</h6>
                        <h3 class="mb-0 text-info" id="total-instances">-</h3>
                        <small class="text-muted">EC2 Instances</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">Running Instances</h6>
                        <h3 class="mb-0 text-success" id="running-instances">-</h3>
                        <small class="text-muted">Active Now</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-play-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">SSM Online</h6>
                        <h3 class="mb-0 text-success" id="ssm-online">-</h3>
                        <small class="text-muted">Agents Connected</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-heartbeat fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Activity -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Instance State Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="stateChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- SSM Status -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">SSM Agent Status</h5>
            </div>
            <div class="card-body">
                <canvas id="ssmChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Instances -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent EC2 Instances</h5>
                <a href="{% url 'inventory:ec2_instances' %}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-compact table-striped table-hover mb-0" id="recent-instances-table">
                        <thead>
                            <tr>
                                <th style="width: 140px;">Instance ID</th>
                                <th style="width: 120px;">Name</th>
                                <th style="width: 80px;">Type</th>
                                <th style="width: 70px;">State</th>
                                <th style="width: 120px;">Account</th>
                                <th style="width: 70px;">Env</th>
                                <th style="width: 100px;">Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Last Refresh Info -->
<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">Last inventory refresh: <span id="last-refresh">-</span></small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="loadDashboardData()">
                            <i class="fas fa-sync-alt"></i> Refresh Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    let stateChart, ssmChart;

    function loadDashboardData() {
        showLoading();
        
        // Load dashboard statistics
        $.ajax({
            url: '/api/dashboard-stats/',
            method: 'GET',
            success: function(data) {
                updateStatistics(data);
                updateCharts(data);
                loadRecentInstances();
                hideLoading();
            },
            error: function(xhr) {
                hideLoading();
                showAlert('Failed to load dashboard data', 'danger');
            }
        });
    }

    function updateStatistics(data) {
        $('#total-accounts').text(data.total_accounts || 0);
        $('#total-instances').text(data.total_instances || 0);
        $('#running-instances').text(data.running_instances || 0);
        $('#ssm-online').text(data.ssm_online || 0);
        $('#last-refresh').text(data.last_refresh ? formatDate(data.last_refresh) : 'Never');
    }

    function updateCharts(data) {
        // Instance State Chart
        const stateCtx = document.getElementById('stateChart').getContext('2d');
        if (stateChart) stateChart.destroy();
        
        stateChart = new Chart(stateCtx, {
            type: 'doughnut',
            data: {
                labels: ['Running', 'Stopped', 'Other'],
                datasets: [{
                    data: [
                        data.running_instances || 0,
                        data.stopped_instances || 0,
                        (data.total_instances || 0) - (data.running_instances || 0) - (data.stopped_instances || 0)
                    ],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // SSM Status Chart
        const ssmCtx = document.getElementById('ssmChart').getContext('2d');
        if (ssmChart) ssmChart.destroy();
        
        ssmChart = new Chart(ssmCtx, {
            type: 'doughnut',
            data: {
                labels: ['Online', 'Offline'],
                datasets: [{
                    data: [
                        data.ssm_online || 0,
                        data.ssm_offline || 0
                    ],
                    backgroundColor: ['#28a745', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    function loadRecentInstances() {
        $.ajax({
            url: '/api/ec2-instances/?page_size=10',
            method: 'GET',
            success: function(data) {
                const tbody = $('#recent-instances-table tbody');
                tbody.empty();
                
                if (data.results && data.results.length > 0) {
                    data.results.forEach(function(instance) {
                        const row = `
                            <tr>
                                <td><code class="small">${instance.instance_id}</code></td>
                                <td class="text-truncate" style="max-width: 120px;" title="${instance.name || 'No name'}">${truncateText(instance.name || '-', 15)}</td>
                                <td><small>${instance.instance_type}</small></td>
                                <td><span class="badge badge-sm bg-${getStateColor(instance.state)}">${instance.state}</span></td>
                                <td class="text-truncate" style="max-width: 120px;" title="${instance.account_name}">${truncateText(instance.account_name, 15)}</td>
                                <td><small>${instance.env_tag || '-'}</small></td>
                                <td><small>${formatDate(instance.last_updated)}</small></td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.append('<tr><td colspan="7" class="text-center">No instances found</td></tr>');
                }
            },
            error: function(xhr) {
                showAlert('Failed to load recent instances', 'warning');
            }
        });
    }

    function getStateColor(state) {
        switch(state) {
            case 'running': return 'success';
            case 'stopped': return 'danger';
            case 'pending': return 'warning';
            case 'stopping': return 'warning';
            case 'terminated': return 'dark';
            default: return 'secondary';
        }
    }

    function truncateText(text, maxLength) {
        if (!text || text === '-') return text;
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    // Load data when page loads
    $(document).ready(function() {
        loadDashboardData();
        
        // Auto-refresh every 5 minutes
        setInterval(loadDashboardData, 300000);
    });

    // Make loadData available globally for base template
    window.loadData = loadDashboardData;
</script>
{% endblock %}
