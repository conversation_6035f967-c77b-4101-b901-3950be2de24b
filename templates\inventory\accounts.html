{% extends 'inventory/base.html' %}

{% block title %}AWS Accounts - Cloud Operations Central{% endblock %}
{% block page_title %}AWS Accounts Manager{% endblock %}

{% block page_actions %}
<div class="btn-group me-2">
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#accountModal">
        <i class="fas fa-plus me-1"></i>
        Add Account
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="loadAccounts()">
        <i class="fas fa-sync-alt me-1"></i>
        Refresh
    </button>
</div>
<button type="button" class="btn btn-success" onclick="refreshAllInventory()">
    <i class="fas fa-sync-alt me-1"></i>
    Refresh All
</button>
{% endblock %}

{% block content %}
<!-- Statistics Dashboard -->
<div class="row mb-4" id="stats-dashboard" style="display: none;">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3 class="mb-1" id="total-accounts">0</h3>
                <small>Total Accounts</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3 class="mb-1" id="active-accounts">0</h3>
                <small>Active Accounts</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3 class="mb-1" id="inactive-accounts">0</h3>
                <small>Inactive Accounts</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3 class="mb-1" id="business-units-count">0</h3>
                <small>Business Units</small>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Search and Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>Search & Filter AWS Accounts
        </h6>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <!-- Search Box -->
            <div class="col-md-6">
                <label for="search-input" class="form-label">Search</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="search-input"
                           placeholder="Search by account name, ID, business unit, or region...">
                    <button class="btn btn-outline-secondary" type="button" id="clear-search">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Business Unit Filter -->
            <div class="col-md-2">
                <label for="filter-bu" class="form-label">Business Unit</label>
                <select class="form-select" id="filter-bu">
                    <option value="">All Business Units</option>
                </select>
            </div>

            <!-- Region Filter -->
            <div class="col-md-2">
                <label for="filter-region" class="form-label">Region</label>
                <select class="form-select" id="filter-region">
                    <option value="">All Regions</option>
                </select>
            </div>

            <!-- Status Filter -->
            <div class="col-md-2">
                <label for="filter-status" class="form-label">Status</label>
                <select class="form-select" id="filter-status">
                    <option value="">All Status</option>
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                </select>
            </div>
        </div>

        <!-- Filter Tags -->
        <div class="mt-3" id="filter-tags" style="display: none;">
            <small class="text-muted">Active Filters:</small>
            <div id="filter-tags-container" class="d-inline-block ms-2"></div>
        </div>

        <!-- Quick Stats -->
        <div class="mt-3" id="quick-stats" style="display: none;">
            <div class="d-flex flex-wrap gap-2">
                <span class="badge bg-light text-dark" id="showing-results">Showing 0 results</span>
                <span class="badge bg-primary" id="search-results" style="display: none;"></span>
            </div>
        </div>
    </div>
</div>

<!-- Top Business Units and Regions -->
<div class="row mb-4" id="top-lists" style="display: none;">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>Top Business Units
                </h6>
            </div>
            <div class="card-body">
                <div id="top-business-units">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-globe me-2"></i>Top Regions
                </h6>
            </div>
            <div class="card-body">
                <div id="top-regions">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Accounts Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>AWS Accounts
        </h6>
        <div class="d-flex align-items-center gap-2">
            <span class="badge bg-primary" id="account-count">0 accounts</span>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary" id="export-csv">
                    <i class="fas fa-download me-1"></i>Export CSV
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="loadAccounts()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="accounts-table">
                <thead>
                    <tr>
                        <th>
                            <a href="#" class="text-decoration-none" data-sort="account_name">
                                Account Name <i class="fas fa-sort text-muted"></i>
                            </a>
                        </th>
                        <th>
                            <a href="#" class="text-decoration-none" data-sort="account_id">
                                Account ID <i class="fas fa-sort text-muted"></i>
                            </a>
                        </th>
                        <th>
                            <a href="#" class="text-decoration-none" data-sort="business_unit">
                                Business Unit <i class="fas fa-sort text-muted"></i>
                            </a>
                        </th>
                        <th>
                            <a href="#" class="text-decoration-none" data-sort="region">
                                Region <i class="fas fa-sort text-muted"></i>
                            </a>
                        </th>
                        <th>EC2 Instances</th>
                        <th>
                            <a href="#" class="text-decoration-none" data-sort="is_active">
                                Status <i class="fas fa-sort text-muted"></i>
                            </a>
                        </th>
                        <th>
                            <a href="#" class="text-decoration-none" data-sort="created_at">
                                Created <i class="fas fa-sort text-muted"></i>
                            </a>
                        </th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <nav aria-label="Accounts pagination" id="pagination-container" style="display: none;">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Pagination will be generated by JavaScript -->
            </ul>
        </nav>
    </div>
</div>

<!-- Add/Edit Account Modal -->
<div class="modal fade" id="accountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="accountModalTitle">Add AWS Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="accountForm">
                <div class="modal-body">
                    <input type="hidden" id="account-id-hidden" name="id">
                    
                    <div class="mb-3">
                        <label for="account-id" class="form-label">Account ID *</label>
                        <input type="text" class="form-control" id="account-id" name="account_id" required 
                               pattern="[0-9]{12}" title="Account ID must be 12 digits">
                    </div>
                    
                    <div class="mb-3">
                        <label for="account-name" class="form-label">Account Name</label>
                        <input type="text" class="form-control" id="account-name" name="account_name">
                    </div>
                    
                    <div class="mb-3">
                        <label for="business-unit" class="form-label">Business Unit *</label>
                        <input type="text" class="form-control" id="business-unit" name="business_unit" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="region" class="form-label">Region *</label>
                        <select class="form-select" id="region" name="region" required>
                            <option value="">Select Region</option>
                            <option value="us-east-1">US East (N. Virginia)</option>
                            <option value="us-east-2">US East (Ohio)</option>
                            <option value="us-west-1">US West (N. California)</option>
                            <option value="us-west-2">US West (Oregon)</option>
                            <option value="eu-west-1">Europe (Ireland)</option>
                            <option value="eu-west-2">Europe (London)</option>
                            <option value="eu-central-1">Europe (Frankfurt)</option>
                            <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                            <option value="ap-southeast-2">Asia Pacific (Sydney)</option>
                            <option value="ap-northeast-1">Asia Pacific (Tokyo)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is-active" name="is_active" checked>
                            <label class="form-check-label" for="is-active">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Account</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Confirm Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this account?</p>
                <p><strong>Warning:</strong> This will also delete all associated EC2 instances and SSM status data.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let editingAccountId = null;
    let currentFilters = {};
    let currentSort = 'business_unit,account_name';
    let currentPage = 1;
    let allStatistics = {};

    function loadAccounts(page = 1) {
        showLoading();

        // Build query parameters
        const params = new URLSearchParams();

        // Add search
        const search = $('#search-input').val().trim();
        if (search) {
            params.append('search', search);
        }

        // Add filters
        const businessUnit = $('#filter-bu').val();
        if (businessUnit) {
            params.append('business_unit', businessUnit);
        }

        const region = $('#filter-region').val();
        if (region) {
            params.append('region', region);
        }

        const status = $('#filter-status').val();
        if (status) {
            params.append('is_active', status);
        }

        // Add sorting
        if (currentSort) {
            params.append('ordering', currentSort);
        }

        // Add pagination
        params.append('page', page);
        params.append('page_size', 25);

        $.ajax({
            url: '/api/accounts/?' + params.toString(),
            method: 'GET',
            success: function(data) {
                updateAccountsTable(data.results);
                updateAccountCount(data.count);
                updateStatistics(data.statistics);
                updateFilterOptions(data.statistics);
                updatePagination(data);
                updateFilterTags();
                updateQuickStats(data);
                hideLoading();
            },
            error: function(xhr) {
                hideLoading();
                showAlert('Failed to load accounts', 'danger');
            }
        });
    }

    function updateAccountsTable(accounts) {
        const tbody = $('#accounts-table tbody');
        tbody.empty();

        if (!accounts || accounts.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="8" class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        No AWS accounts found
                        ${getNoResultsMessage()}
                    </td>
                </tr>
            `);
            return;
        }

        accounts.forEach(function(account) {
            const statusBadge = account.is_active
                ? '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Active</span>'
                : '<span class="badge bg-secondary"><i class="fas fa-times-circle me-1"></i>Inactive</span>';

            const createdDate = formatDate(account.created_at);

            // Enhanced business unit display with colors
            const buColors = {
                'HQ CTO Vernova': 'primary',
                'Gas Power': 'success',
                'Renewable Energy': 'info',
                'Wind Power': 'warning',
                'Hydro Power': 'secondary',
                'Nuclear Power': 'danger',
                'Solar Energy': 'warning',
                'Grid Solutions': 'dark',
                'Digital Services': 'secondary',
                'Energy Transition': 'info'
            };
            const buColor = buColors[account.business_unit] || 'primary';

            // Enhanced region display
            const regionFlags = {
                'us-east-1': '🇺🇸 N. Virginia',
                'us-east-2': '🇺🇸 Ohio',
                'us-west-1': '🇺🇸 N. California',
                'us-west-2': '🇺🇸 Oregon',
                'eu-west-1': '🇮🇪 Ireland',
                'eu-west-2': '🇬🇧 London',
                'eu-west-3': '🇫🇷 Paris',
                'eu-central-1': '🇩🇪 Frankfurt',
                'ap-southeast-1': '🇸🇬 Singapore',
                'ap-southeast-2': '🇦🇺 Sydney',
                'ap-northeast-1': '🇯🇵 Tokyo',
                'ap-northeast-2': '🇰🇷 Seoul',
                'ca-central-1': '🇨🇦 Canada',
                'sa-east-1': '🇧🇷 São Paulo'
            };
            const regionDisplay = regionFlags[account.region] || `🌍 ${account.region}`;

            const row = `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            ${account.is_active ? '<i class="fas fa-check-circle text-success me-2"></i>' : '<i class="fas fa-times-circle text-muted me-2"></i>'}
                            <strong>${account.account_name}</strong>
                        </div>
                    </td>
                    <td><code class="bg-light px-2 py-1 rounded">${account.account_id}</code></td>
                    <td><span class="badge bg-${buColor}">${account.business_unit}</span></td>
                    <td><span title="${account.region}">${regionDisplay}</span></td>
                    <td><span class="badge bg-info">${account.ec2_instances_count || 0}</span></td>
                    <td>${statusBadge}</td>
                    <td><small class="text-muted">${createdDate}</small></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editAccount(${account.id})" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="refreshAccount(${account.id})" title="Refresh Inventory">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    function updateAccountCount(count) {
        $('#account-count').text(`${count} accounts`);
    }

    function showAccountModal(account = null) {
        if (account) {
            // Edit mode
            editingAccountId = account.id;
            $('#accountModalTitle').text('Edit AWS Account');
            $('#account-id-hidden').val(account.id);
            $('#account-id').val(account.account_id).prop('readonly', true);
            $('#account-name').val(account.account_name);
            $('#business-unit').val(account.business_unit);
            $('#region').val(account.region);
            $('#is-active').prop('checked', account.is_active);
        } else {
            // Add mode
            editingAccountId = null;
            $('#accountModalTitle').text('Add AWS Account');
            $('#accountForm')[0].reset();
            $('#account-id').prop('readonly', false);
            $('#is-active').prop('checked', true);
        }
        
        $('#accountModal').modal('show');
    }

    function editAccount(accountId) {
        $.ajax({
            url: `/api/accounts/${accountId}/`,
            method: 'GET',
            success: function(account) {
                showAccountModal(account);
            },
            error: function(xhr) {
                showAlert('Failed to load account details', 'danger');
            }
        });
    }

    function refreshAccount(accountId) {
        if (confirm('This will refresh inventory for this account. Continue?')) {
            showLoading();
            
            $.ajax({
                url: `/api/accounts/${accountId}/refresh_inventory/`,
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrftoken
                },
                success: function(response) {
                    hideLoading();
                    showAlert(response.message, 'success');
                    loadAccounts();
                },
                error: function(xhr) {
                    hideLoading();
                    const response = xhr.responseJSON || {};
                    showAlert(response.message || 'Failed to refresh account inventory', 'danger');
                }
            });
        }
    }

    function deleteAccount(accountId) {
        editingAccountId = accountId;
        $('#deleteModal').modal('show');
    }

    // Form submission
    $('#accountForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            account_id: $('#account-id').val(),
            account_name: $('#account-name').val(),
            business_unit: $('#business-unit').val(),
            region: $('#region').val(),
            is_active: $('#is-active').is(':checked')
        };
        
        const url = editingAccountId ? `/api/accounts/${editingAccountId}/` : '/api/accounts/';
        const method = editingAccountId ? 'PUT' : 'POST';
        
        $.ajax({
            url: url,
            method: method,
            headers: {
                'X-CSRFToken': csrftoken,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(formData),
            success: function(response) {
                $('#accountModal').modal('hide');
                showAlert(`Account ${editingAccountId ? 'updated' : 'created'} successfully`, 'success');
                loadAccounts();
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                let errorMessage = 'Failed to save account';
                
                if (response.account_id) {
                    errorMessage = 'Account ID: ' + response.account_id.join(', ');
                } else if (response.detail) {
                    errorMessage = response.detail;
                }
                
                showAlert(errorMessage, 'danger');
            }
        });
    });

    // Confirm delete
    $('#confirmDelete').on('click', function() {
        if (editingAccountId) {
            $.ajax({
                url: `/api/accounts/${editingAccountId}/`,
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': csrftoken
                },
                success: function() {
                    $('#deleteModal').modal('hide');
                    showAlert('Account deleted successfully', 'success');
                    loadAccounts();
                },
                error: function(xhr) {
                    showAlert('Failed to delete account', 'danger');
                }
            });
        }
    });

    function getNoResultsMessage() {
        const search = $('#search-input').val().trim();
        const hasFilters = $('#filter-bu').val() || $('#filter-region').val() || $('#filter-status').val();

        if (search && hasFilters) {
            return `<br><small class="text-muted">Try adjusting your search term or filters</small>`;
        } else if (search) {
            return `<br><small class="text-muted">No accounts match "${search}"</small>`;
        } else if (hasFilters) {
            return `<br><small class="text-muted">No accounts match the selected filters</small>`;
        }
        return '';
    }

    function updateStatistics(stats) {
        if (!stats) return;

        allStatistics = stats;

        // Update dashboard cards
        $('#total-accounts').text(stats.total_accounts || 0);
        $('#active-accounts').text(stats.active_accounts || 0);
        $('#inactive-accounts').text(stats.inactive_accounts || 0);
        $('#business-units-count').text(stats.business_units ? stats.business_units.length : 0);

        // Show dashboard
        $('#stats-dashboard').show();

        // Update top lists
        updateTopLists(stats);
    }

    function updateTopLists(stats) {
        if (!stats) return;

        // Top business units
        const buContainer = $('#top-business-units');
        buContainer.empty();

        if (stats.business_units && stats.business_units.length > 0) {
            stats.business_units.slice(0, 5).forEach(function(bu) {
                buContainer.append(`
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-truncate me-2">${bu.business_unit}</span>
                        <span class="badge bg-primary">${bu.count}</span>
                    </div>
                `);
            });
        } else {
            buContainer.append('<small class="text-muted">No data available</small>');
        }

        // Top regions
        const regionContainer = $('#top-regions');
        regionContainer.empty();

        if (stats.regions && stats.regions.length > 0) {
            stats.regions.slice(0, 5).forEach(function(region) {
                const regionFlags = {
                    'us-east-1': '🇺🇸',
                    'us-east-2': '🇺🇸',
                    'us-west-1': '🇺🇸',
                    'us-west-2': '🇺🇸',
                    'eu-west-1': '🇮🇪',
                    'eu-west-2': '🇬🇧',
                    'eu-west-3': '🇫🇷',
                    'eu-central-1': '🇩🇪',
                    'ap-southeast-1': '🇸🇬',
                    'ap-southeast-2': '🇦🇺',
                    'ap-northeast-1': '🇯🇵',
                    'ap-northeast-2': '🇰🇷',
                    'ca-central-1': '🇨🇦',
                    'sa-east-1': '🇧🇷'
                };
                const flag = regionFlags[region.region] || '🌍';

                regionContainer.append(`
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-truncate me-2">${flag} ${region.region}</span>
                        <span class="badge bg-info">${region.count}</span>
                    </div>
                `);
            });
        } else {
            regionContainer.append('<small class="text-muted">No data available</small>');
        }

        // Show top lists
        $('#top-lists').show();
    }

    function updateFilterOptions(stats) {
        if (!stats) return;

        // Update business unit filter
        const buSelect = $('#filter-bu');
        const currentBU = buSelect.val();
        buSelect.find('option:not(:first)').remove();

        if (stats.business_units) {
            stats.business_units.forEach(function(bu) {
                buSelect.append(`<option value="${bu.business_unit}">${bu.business_unit} (${bu.count})</option>`);
            });
        }
        buSelect.val(currentBU);

        // Update region filter
        const regionSelect = $('#filter-region');
        const currentRegion = regionSelect.val();
        regionSelect.find('option:not(:first)').remove();

        if (stats.regions) {
            stats.regions.forEach(function(region) {
                regionSelect.append(`<option value="${region.region}">${region.region} (${region.count})</option>`);
            });
        }
        regionSelect.val(currentRegion);
    }

    function updateFilterTags() {
        const container = $('#filter-tags-container');
        const tagsDiv = $('#filter-tags');
        container.empty();

        let hasFilters = false;

        // Search tag
        const search = $('#search-input').val().trim();
        if (search) {
            container.append(`
                <span class="badge bg-primary me-1">
                    Search: "${search}"
                    <button type="button" class="btn-close btn-close-white ms-1" onclick="clearSearch()"></button>
                </span>
            `);
            hasFilters = true;
        }

        // Business unit tag
        const bu = $('#filter-bu').val();
        if (bu) {
            container.append(`
                <span class="badge bg-success me-1">
                    BU: ${bu}
                    <button type="button" class="btn-close btn-close-white ms-1" onclick="clearFilter('bu')"></button>
                </span>
            `);
            hasFilters = true;
        }

        // Region tag
        const region = $('#filter-region').val();
        if (region) {
            container.append(`
                <span class="badge bg-info me-1">
                    Region: ${region}
                    <button type="button" class="btn-close btn-close-white ms-1" onclick="clearFilter('region')"></button>
                </span>
            `);
            hasFilters = true;
        }

        // Status tag
        const status = $('#filter-status').val();
        if (status) {
            const statusText = status === 'true' ? 'Active' : 'Inactive';
            container.append(`
                <span class="badge bg-warning me-1">
                    Status: ${statusText}
                    <button type="button" class="btn-close btn-close-white ms-1" onclick="clearFilter('status')"></button>
                </span>
            `);
            hasFilters = true;
        }

        // Clear all button
        if (hasFilters) {
            container.append(`
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllFilters()">
                    <i class="fas fa-times me-1"></i>Clear All
                </button>
            `);
        }

        tagsDiv.toggle(hasFilters);
    }

    function updateQuickStats(data) {
        const quickStats = $('#quick-stats');
        const showingResults = $('#showing-results');
        const searchResults = $('#search-results');

        if (data.count !== undefined) {
            showingResults.text(`Showing ${data.count} results`);

            const search = $('#search-input').val().trim();
            if (search) {
                searchResults.text(`Found ${data.count} matches for "${search}"`).show();
            } else {
                searchResults.hide();
            }

            quickStats.show();
        }
    }

    function clearSearch() {
        $('#search-input').val('');
        loadAccounts();
    }

    function clearFilter(type) {
        if (type === 'bu') {
            $('#filter-bu').val('');
        } else if (type === 'region') {
            $('#filter-region').val('');
        } else if (type === 'status') {
            $('#filter-status').val('');
        }
        loadAccounts();
    }

    function clearAllFilters() {
        $('#search-input').val('');
        $('#filter-bu').val('');
        $('#filter-region').val('');
        $('#filter-status').val('');
        loadAccounts();
    }

    function updatePagination(data) {
        const container = $('#pagination-container');
        const pagination = $('#pagination');

        if (!data.next && !data.previous) {
            container.hide();
            return;
        }

        pagination.empty();

        // Previous button
        if (data.previous) {
            pagination.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadAccounts(${currentPage - 1})">Previous</a>
                </li>
            `);
        }

        // Page numbers (simplified)
        pagination.append(`
            <li class="page-item active">
                <span class="page-link">Page ${currentPage}</span>
            </li>
        `);

        // Next button
        if (data.next) {
            pagination.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadAccounts(${currentPage + 1})">Next</a>
                </li>
            `);
        }

        container.show();
    }

    // Load data when page loads
    $(document).ready(function() {
        loadAccounts();

        // Search input handler
        let searchTimeout;
        $('#search-input').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                currentPage = 1;
                loadAccounts();
            }, 500);
        });

        // Filter change handlers
        $('#filter-bu, #filter-region, #filter-status').on('change', function() {
            currentPage = 1;
            loadAccounts();
        });

        // Clear search button
        $('#clear-search').on('click', function() {
            clearSearch();
        });

        // Sort handlers
        $('[data-sort]').on('click', function(e) {
            e.preventDefault();
            const field = $(this).data('sort');

            // Toggle sort direction
            if (currentSort === field) {
                currentSort = '-' + field;
            } else if (currentSort === '-' + field) {
                currentSort = field;
            } else {
                currentSort = field;
            }

            // Update sort icons
            $('[data-sort] i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
            if (currentSort.startsWith('-')) {
                $(this).find('i').removeClass('fa-sort fa-sort-up').addClass('fa-sort-down');
            } else {
                $(this).find('i').removeClass('fa-sort fa-sort-down').addClass('fa-sort-up');
            }

            loadAccounts();
        });

        // Export CSV handler
        $('#export-csv').on('click', function() {
            const params = new URLSearchParams();

            const search = $('#search-input').val().trim();
            if (search) params.append('search', search);

            const businessUnit = $('#filter-bu').val();
            if (businessUnit) params.append('business_unit', businessUnit);

            const region = $('#filter-region').val();
            if (region) params.append('region', region);

            const status = $('#filter-status').val();
            if (status) params.append('is_active', status);

            params.append('format', 'csv');
            params.append('page_size', '1000');

            window.open('/api/accounts/?' + params.toString(), '_blank');
        });

        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Ctrl+F or Cmd+F to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                $('#search-input').focus().select();
            }

            // Escape to clear search
            if (e.key === 'Escape' && document.activeElement === document.getElementById('search-input')) {
                if ($('#search-input').val()) {
                    clearSearch();
                } else {
                    $('#search-input').blur();
                }
            }
        });
    });

    // Make loadData available globally for base template
    window.loadData = loadAccounts;
</script>
{% endblock %}
