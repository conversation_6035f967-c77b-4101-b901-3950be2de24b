{% extends 'inventory/base.html' %}

{% block title %}AWS Accounts - Cloud Operations Central{% endblock %}
{% block page_title %}AWS Accounts Manager{% endblock %}

{% block page_actions %}
<div class="btn-group me-2">
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#accountModal">
        <i class="fas fa-plus me-1"></i>
        Add Account
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="loadAccounts()">
        <i class="fas fa-sync-alt me-1"></i>
        Refresh
    </button>
</div>
<button type="button" class="btn btn-success" onclick="refreshAllInventory()">
    <i class="fas fa-sync-alt me-1"></i>
    Refresh All
</button>
{% endblock %}

{% block content %}
<!-- Accounts Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="card-title mb-0">AWS Accounts</h6>
        <span class="badge bg-primary" id="account-count">0 accounts</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="accounts-table">
                <thead>
                    <tr>
                        <th>Account Name</th>
                        <th>Account ID</th>
                        <th>Business Unit</th>
                        <th>Region</th>
                        <th>EC2 Instances</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Account Modal -->
<div class="modal fade" id="accountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="accountModalTitle">Add AWS Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="accountForm">
                <div class="modal-body">
                    <input type="hidden" id="account-id-hidden" name="id">
                    
                    <div class="mb-3">
                        <label for="account-id" class="form-label">Account ID *</label>
                        <input type="text" class="form-control" id="account-id" name="account_id" required 
                               pattern="[0-9]{12}" title="Account ID must be 12 digits">
                    </div>
                    
                    <div class="mb-3">
                        <label for="account-name" class="form-label">Account Name</label>
                        <input type="text" class="form-control" id="account-name" name="account_name">
                    </div>
                    
                    <div class="mb-3">
                        <label for="business-unit" class="form-label">Business Unit *</label>
                        <input type="text" class="form-control" id="business-unit" name="business_unit" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="region" class="form-label">Region *</label>
                        <select class="form-select" id="region" name="region" required>
                            <option value="">Select Region</option>
                            <option value="us-east-1">US East (N. Virginia)</option>
                            <option value="us-east-2">US East (Ohio)</option>
                            <option value="us-west-1">US West (N. California)</option>
                            <option value="us-west-2">US West (Oregon)</option>
                            <option value="eu-west-1">Europe (Ireland)</option>
                            <option value="eu-west-2">Europe (London)</option>
                            <option value="eu-central-1">Europe (Frankfurt)</option>
                            <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                            <option value="ap-southeast-2">Asia Pacific (Sydney)</option>
                            <option value="ap-northeast-1">Asia Pacific (Tokyo)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is-active" name="is_active" checked>
                            <label class="form-check-label" for="is-active">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Account</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Confirm Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this account?</p>
                <p><strong>Warning:</strong> This will also delete all associated EC2 instances and SSM status data.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let editingAccountId = null;

    function loadAccounts() {
        showLoading();
        
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            success: function(data) {
                updateAccountsTable(data.results);
                updateAccountCount(data.count);
                hideLoading();
            },
            error: function(xhr) {
                hideLoading();
                showAlert('Failed to load accounts', 'danger');
            }
        });
    }

    function updateAccountsTable(accounts) {
        const tbody = $('#accounts-table tbody');
        tbody.empty();
        
        if (accounts && accounts.length > 0) {
            accounts.forEach(function(account) {
                const row = `
                    <tr>
                        <td>${account.account_name || '-'}</td>
                        <td><code>${account.account_id}</code></td>
                        <td>${account.business_unit}</td>
                        <td>${account.region}</td>
                        <td>
                            <span class="badge bg-info">${account.ec2_instances_count || 0}</span>
                        </td>
                        <td>
                            <span class="badge bg-${account.is_active ? 'success' : 'secondary'}">
                                ${account.is_active ? 'Active' : 'Inactive'}
                            </span>
                        </td>
                        <td>${formatDate(account.created_at)}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editAccount(${account.id})" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="refreshAccount(${account.id})" title="Refresh Inventory">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteAccount(${account.id})" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="8" class="text-center">No accounts found</td></tr>');
        }
    }

    function updateAccountCount(count) {
        $('#account-count').text(`${count} accounts`);
    }

    function showAccountModal(account = null) {
        if (account) {
            // Edit mode
            editingAccountId = account.id;
            $('#accountModalTitle').text('Edit AWS Account');
            $('#account-id-hidden').val(account.id);
            $('#account-id').val(account.account_id).prop('readonly', true);
            $('#account-name').val(account.account_name);
            $('#business-unit').val(account.business_unit);
            $('#region').val(account.region);
            $('#is-active').prop('checked', account.is_active);
        } else {
            // Add mode
            editingAccountId = null;
            $('#accountModalTitle').text('Add AWS Account');
            $('#accountForm')[0].reset();
            $('#account-id').prop('readonly', false);
            $('#is-active').prop('checked', true);
        }
        
        $('#accountModal').modal('show');
    }

    function editAccount(accountId) {
        $.ajax({
            url: `/api/accounts/${accountId}/`,
            method: 'GET',
            success: function(account) {
                showAccountModal(account);
            },
            error: function(xhr) {
                showAlert('Failed to load account details', 'danger');
            }
        });
    }

    function refreshAccount(accountId) {
        if (confirm('This will refresh inventory for this account. Continue?')) {
            showLoading();
            
            $.ajax({
                url: `/api/accounts/${accountId}/refresh_inventory/`,
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrftoken
                },
                success: function(response) {
                    hideLoading();
                    showAlert(response.message, 'success');
                    loadAccounts();
                },
                error: function(xhr) {
                    hideLoading();
                    const response = xhr.responseJSON || {};
                    showAlert(response.message || 'Failed to refresh account inventory', 'danger');
                }
            });
        }
    }

    function deleteAccount(accountId) {
        editingAccountId = accountId;
        $('#deleteModal').modal('show');
    }

    // Form submission
    $('#accountForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            account_id: $('#account-id').val(),
            account_name: $('#account-name').val(),
            business_unit: $('#business-unit').val(),
            region: $('#region').val(),
            is_active: $('#is-active').is(':checked')
        };
        
        const url = editingAccountId ? `/api/accounts/${editingAccountId}/` : '/api/accounts/';
        const method = editingAccountId ? 'PUT' : 'POST';
        
        $.ajax({
            url: url,
            method: method,
            headers: {
                'X-CSRFToken': csrftoken,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(formData),
            success: function(response) {
                $('#accountModal').modal('hide');
                showAlert(`Account ${editingAccountId ? 'updated' : 'created'} successfully`, 'success');
                loadAccounts();
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                let errorMessage = 'Failed to save account';
                
                if (response.account_id) {
                    errorMessage = 'Account ID: ' + response.account_id.join(', ');
                } else if (response.detail) {
                    errorMessage = response.detail;
                }
                
                showAlert(errorMessage, 'danger');
            }
        });
    });

    // Confirm delete
    $('#confirmDelete').on('click', function() {
        if (editingAccountId) {
            $.ajax({
                url: `/api/accounts/${editingAccountId}/`,
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': csrftoken
                },
                success: function() {
                    $('#deleteModal').modal('hide');
                    showAlert('Account deleted successfully', 'success');
                    loadAccounts();
                },
                error: function(xhr) {
                    showAlert('Failed to delete account', 'danger');
                }
            });
        }
    });

    // Load data when page loads
    $(document).ready(function() {
        loadAccounts();
    });

    // Make loadData available globally for base template
    window.loadData = loadAccounts;
</script>
{% endblock %}
