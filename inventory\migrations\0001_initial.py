# Generated by Django 4.2.7 on 2025-06-11 12:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AWSAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_id', models.CharField(max_length=12, unique=True)),
                ('account_name', models.CharField(blank=True, max_length=255)),
                ('region', models.CharField(max_length=50)),
                ('business_unit', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['business_unit', 'account_name'],
            },
        ),
        migrations.CreateModel(
            name='EC2Instance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('instance_id', models.CharField(max_length=20)),
                ('instance_type', models.CharField(max_length=50)),
                ('state', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('shutting-down', 'Shutting Down'), ('terminated', 'Terminated'), ('stopping', 'Stopping'), ('stopped', 'Stopped')], max_length=20)),
                ('name', models.CharField(blank=True, max_length=255)),
                ('private_ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('private_dns_name', models.CharField(blank=True, max_length=255)),
                ('vcpu_count', models.IntegerField(blank=True, null=True)),
                ('memory_gb', models.FloatField(blank=True, null=True)),
                ('root_volume_id', models.CharField(blank=True, max_length=50)),
                ('root_volume_size_gb', models.IntegerField(blank=True, null=True)),
                ('data_disk_count', models.IntegerField(default=0)),
                ('os_information', models.CharField(blank=True, max_length=255)),
                ('platform_type', models.CharField(blank=True, max_length=50)),
                ('env_tag', models.CharField(blank=True, max_length=100)),
                ('uai_tag', models.CharField(blank=True, max_length=100)),
                ('patch_tag', models.CharField(blank=True, max_length=100)),
                ('app_env_cfg_id', models.CharField(blank=True, max_length=100)),
                ('maintenance_schedule', models.CharField(blank=True, max_length=100)),
                ('schedule_tag', models.CharField(blank=True, max_length=100)),
                ('backup_tag', models.CharField(blank=True, max_length=100)),
                ('cto_cloud_ops_managed', models.CharField(default='NotAssigned', max_length=100)),
                ('patch_provider', models.CharField(default='NotAssigned', max_length=100)),
                ('patch_group', models.CharField(default='NotAssigned', max_length=100)),
                ('patch_exempt', models.CharField(default='NotAssigned', max_length=100)),
                ('all_tags', models.TextField(blank=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ec2_instances', to='inventory.awsaccount')),
            ],
            options={
                'ordering': ['-last_updated'],
                'unique_together': {('account', 'instance_id')},
            },
        ),
        migrations.CreateModel(
            name='SSMStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ping_status', models.CharField(blank=True, choices=[('Online', 'Online'), ('Connection Lost', 'Connection Lost'), ('Inactive', 'Inactive')], max_length=20)),
                ('agent_version', models.CharField(blank=True, max_length=50)),
                ('is_latest_version', models.BooleanField(blank=True, null=True)),
                ('last_ping_datetime', models.DateTimeField(blank=True, null=True)),
                ('platform_name', models.CharField(blank=True, max_length=100)),
                ('platform_type', models.CharField(blank=True, max_length=50)),
                ('ssm_configured', models.BooleanField(default=False)),
                ('last_checked', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('instance', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='ssm_status', to='inventory.ec2instance')),
            ],
            options={
                'ordering': ['-last_checked'],
            },
        ),
        migrations.CreateModel(
            name='InventoryRefreshLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='running', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('instances_processed', models.IntegerField(default=0)),
                ('errors_count', models.IntegerField(default=0)),
                ('error_details', models.TextField(blank=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refresh_logs', to='inventory.awsaccount')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
    ]
