from rest_framework import serializers
from .models import AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog, InstanceTag
import json


class InstanceTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = InstanceTag
        fields = ['key', 'value']


class AWSAccountSerializer(serializers.ModelSerializer):
    ec2_instances_count = serializers.SerializerMethodField()

    class Meta:
        model = AWSAccount
        fields = '__all__'

    def get_ec2_instances_count(self, obj):
        return obj.ec2_instances.count()


class SSMStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = SSMStatus
        fields = '__all__'


class EC2InstanceMainInventorySerializer(serializers.ModelSerializer):
    """Main inventory table serializer with core fields"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.Char<PERSON>ield(source='account.account_id', read_only=True)
    region = serializers.Char<PERSON>ield(source='account.region', read_only=True)
    ssm_status = serializers.Char<PERSON>ield(source='ssm_status.ping_status', read_only=True)

    class Meta:
        model = EC2Instance
        fields = [
            'business_unit', 'account_name', 'account_id', 'region',
            'instance_id', 'state', 'name', 'os_information',
            'private_ip_address', 'instance_type', 'vcpu_count',
            'memory_gb', 'ssm_status'
        ]


class EC2InstanceDetailedSerializer(serializers.ModelSerializer):
    """Detailed instance view serializer with all fields"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    ssm_status = SSMStatusSerializer(read_only=True)
    instance_tags = InstanceTagSerializer(source='tags', many=True, read_only=True)
    security_groups_list = serializers.SerializerMethodField()

    class Meta:
        model = EC2Instance
        fields = [
            # Core identification
            'business_unit', 'account_name', 'account_id', 'region',
            'instance_id', 'state', 'name', 'os_information',

            # Network and access
            'private_ip_address', 'instance_type', 'vcpu_count', 'memory_gb',

            # Storage
            'root_volume_id', 'root_volume_size_gb', 'data_disk_count',

            # Tags
            'env_tag', 'uai_tag', 'patch_tag', 'app_env_cfg_id',
            'maintenance_schedule', 'schedule_tag', 'backup_tag',
            'cto_cloud_ops_managed', 'patch_provider', 'patch_group', 'patch_exempt',

            # Infrastructure details
            'ami_id', 'instance_profile', 'security_groups_list', 'subnet_id', 'subnet_name',

            # SSM and tags
            'ssm_status', 'instance_tags', 'all_tags_raw',

            # Metadata
            'last_updated', 'created_at'
        ]

    def get_security_groups_list(self, obj):
        """Parse security groups from JSON string"""
        try:
            if obj.security_groups:
                return json.loads(obj.security_groups)
            return []
        except (json.JSONDecodeError, TypeError):
            return []


class EC2InstanceListSerializer(serializers.ModelSerializer):
    """Simplified serializer for list views"""
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    ssm_ping_status = serializers.CharField(source='ssm_status.ping_status', read_only=True)

    class Meta:
        model = EC2Instance
        fields = [
            'id', 'instance_id', 'name', 'instance_type', 'state',
            'private_ip_address', 'os_information', 'env_tag', 'uai_tag',
            'cto_cloud_ops_managed', 'business_unit', 'account_name', 'account_id',
            'region', 'ssm_ping_status', 'last_updated'
        ]


# Backward compatibility
EC2InstanceSerializer = EC2InstanceDetailedSerializer


class InventoryRefreshLogSerializer(serializers.ModelSerializer):
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = InventoryRefreshLog
        fields = '__all__'
    
    def get_duration(self, obj):
        if obj.completed_at and obj.started_at:
            delta = obj.completed_at - obj.started_at
            return str(delta)
        return None


class InventoryStatsSerializer(serializers.Serializer):
    """Serializer for dashboard statistics"""
    total_accounts = serializers.IntegerField()
    total_instances = serializers.IntegerField()
    running_instances = serializers.IntegerField()
    stopped_instances = serializers.IntegerField()
    ssm_online = serializers.IntegerField()
    ssm_offline = serializers.IntegerField()
    last_refresh = serializers.DateTimeField()
