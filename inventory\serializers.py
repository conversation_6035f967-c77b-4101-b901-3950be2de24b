from rest_framework import serializers
from .models import AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog


class AWSAccountSerializer(serializers.ModelSerializer):
    ec2_instances_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AWSAccount
        fields = '__all__'
    
    def get_ec2_instances_count(self, obj):
        return obj.ec2_instances.count()


class SSMStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = SSMStatus
        fields = '__all__'


class EC2InstanceSerializer(serializers.ModelSerializer):
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    ssm_status = SSMStatusSerializer(read_only=True)
    
    class Meta:
        model = EC2Instance
        fields = '__all__'


class EC2InstanceListSerializer(serializers.ModelSerializer):
    """Simplified serializer for list views"""
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    account_id = serializers.CharField(source='account.account_id', read_only=True)
    business_unit = serializers.CharField(source='account.business_unit', read_only=True)
    region = serializers.CharField(source='account.region', read_only=True)
    ssm_ping_status = serializers.CharField(source='ssm_status.ping_status', read_only=True)
    
    class Meta:
        model = EC2Instance
        fields = [
            'id', 'instance_id', 'name', 'instance_type', 'state', 
            'private_ip_address', 'os_information', 'env_tag',
            'cto_cloud_ops_managed', 'account_name', 'account_id', 
            'business_unit', 'region', 'ssm_ping_status', 'last_updated'
        ]


class InventoryRefreshLogSerializer(serializers.ModelSerializer):
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = InventoryRefreshLog
        fields = '__all__'
    
    def get_duration(self, obj):
        if obj.completed_at and obj.started_at:
            delta = obj.completed_at - obj.started_at
            return str(delta)
        return None


class InventoryStatsSerializer(serializers.Serializer):
    """Serializer for dashboard statistics"""
    total_accounts = serializers.IntegerField()
    total_instances = serializers.IntegerField()
    running_instances = serializers.IntegerField()
    stopped_instances = serializers.IntegerField()
    ssm_online = serializers.IntegerField()
    ssm_offline = serializers.IntegerField()
    last_refresh = serializers.DateTimeField()
