<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloud Operations Central - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #017054;
        }
        .sidebar .nav-link {
            color: white;
            padding: 15px 20px;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .main-content {
            padding: 20px;
        }
        .stats-card {
            border-left: 4px solid #017054;
        }
        .demo-badge {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="sidebar-sticky">
                    <div class="text-center py-4">
                        <h4 class="text-white">Cloud Operations Central</h4>
                        <span class="demo-badge">DEMO</span>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#ec2-instances">
                                <i class="fas fa-server me-2"></i>
                                EC2 Instances
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#ssm-status">
                                <i class="fas fa-heartbeat me-2"></i>
                                SSM Status
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#accounts">
                                <i class="fas fa-users me-2"></i>
                                AWS Accounts
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-10 ms-sm-auto main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Cloud Operations Central</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-success">
                            <i class="fas fa-sync-alt me-1"></i>
                            Refresh All
                        </button>
                    </div>
                </div>

                <!-- Alert -->
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Demo Application:</strong> This is a demonstration of the Cloud Operations Central system built with Django, REST API, and modern frontend technologies.
                </div>

                <!-- Statistics Cards -->
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">Total Accounts</h6>
                                        <h3 class="mb-0">4</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">Total Instances</h6>
                                        <h3 class="mb-0">47</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-server fa-2x text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">Running Instances</h6>
                                        <h3 class="mb-0">14</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-play-circle fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">SSM Online</h6>
                                        <h3 class="mb-0">14</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-heartbeat fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Overview -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Key Features</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>EC2 Instance Management</li>
                                    <li><i class="fas fa-check text-success me-2"></i>SSM Agent Monitoring</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Multi-Account Support</li>
                                    <li><i class="fas fa-check text-success me-2"></i>REST API Integration</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Excel Export</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Real-time Dashboard</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Advanced Filtering</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Responsive Design</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Technology Stack</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="fab fa-python text-primary me-2"></i>Django 4.2.7</li>
                                    <li><i class="fas fa-cogs text-info me-2"></i>Django REST Framework</li>
                                    <li><i class="fab fa-aws text-warning me-2"></i>Boto3 AWS SDK</li>
                                    <li><i class="fab fa-bootstrap text-purple me-2"></i>Bootstrap 5.1.3</li>
                                    <li><i class="fab fa-js text-warning me-2"></i>JavaScript/jQuery</li>
                                    <li><i class="fas fa-chart-bar text-success me-2"></i>Chart.js</li>
                                    <li><i class="fas fa-database text-secondary me-2"></i>SQLite/PostgreSQL</li>
                                    <li><i class="fas fa-table text-info me-2"></i>DataTables</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sample Data Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Sample EC2 Instances</h5>
                        <span class="badge bg-primary">47 instances</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Instance ID</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>State</th>
                                        <th>Account</th>
                                        <th>Environment</th>
                                        <th>SSM Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>i-00277446310aa64</code></td>
                                        <td>Server-047</td>
                                        <td>m5.xlarge</td>
                                        <td><span class="badge bg-danger">stopped</span></td>
                                        <td>Staging Account</td>
                                        <td>test</td>
                                        <td><span class="badge bg-success">Online</span></td>
                                    </tr>
                                    <tr>
                                        <td><code>i-0018279d845cb64</code></td>
                                        <td>Server-046</td>
                                        <td>m5.xlarge</td>
                                        <td><span class="badge bg-danger">stopped</span></td>
                                        <td>Staging Account</td>
                                        <td>dev</td>
                                        <td><span class="badge bg-success">Online</span></td>
                                    </tr>
                                    <tr>
                                        <td><code>i-00314ad76a7c330</code></td>
                                        <td>Server-045</td>
                                        <td>t3.micro</td>
                                        <td><span class="badge bg-danger">stopped</span></td>
                                        <td>Staging Account</td>
                                        <td>prod</td>
                                        <td><span class="badge bg-success">Online</span></td>
                                    </tr>
                                    <tr>
                                        <td><code>i-00180783b54f08e</code></td>
                                        <td>Server-044</td>
                                        <td>c5.large</td>
                                        <td><span class="badge bg-warning">pending</span></td>
                                        <td>Staging Account</td>
                                        <td>test</td>
                                        <td><span class="badge bg-warning">Connection Lost</span></td>
                                    </tr>
                                    <tr>
                                        <td><code>i-002317bad16efd7</code></td>
                                        <td>Server-043</td>
                                        <td>m5.large</td>
                                        <td><span class="badge bg-warning">pending</span></td>
                                        <td>Staging Account</td>
                                        <td>staging</td>
                                        <td><span class="badge bg-danger">Inactive</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">How to Run the Application</h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Install Dependencies:</strong> <code>pip install -r requirements.txt</code></li>
                                    <li><strong>Setup Database:</strong> <code>python manage.py migrate</code></li>
                                    <li><strong>Load Sample Data:</strong> <code>python manage.py load_sample_data</code></li>
                                    <li><strong>Create Superuser:</strong> <code>python manage.py createsuperuser</code></li>
                                    <li><strong>Start Server:</strong> <code>python manage.py runserver</code></li>
                                    <li><strong>Access Application:</strong> Open <a href="http://127.0.0.1:8000" target="_blank">http://127.0.0.1:8000</a></li>
                                </ol>
                                
                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Application Status:</strong> All tests passed! The Cloud Operations Central application is fully functional with enhanced UI and ready to use.
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-star me-2"></i>
                                    <strong>New UI Features:</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>Professional header with "Cloud Operations Central" branding</li>
                                        <li>Collapsible sidebar with hierarchical navigation</li>
                                        <li>Modern responsive design with smooth animations</li>
                                        <li>Organized navigation for multi-cloud operations</li>
                                        <li>Placeholder system for future features</li>
                                        <li>Enhanced visual design with professional styling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
