from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON>out<PERSON>
from . import views

# Create router for API endpoints
router = DefaultRouter()
router.register(r'accounts', views.AWSAccountViewSet)
router.register(r'ec2-instances', views.EC2InstanceViewSet)
router.register(r'ssm-status', views.SSMStatusViewSet)

app_name = 'inventory'

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    path('api/dashboard-stats/', views.dashboard_stats, name='dashboard_stats'),
    path('api/refresh-all/', views.refresh_all_inventory, name='refresh_all_inventory'),
    path('api/available-tags/', views.get_available_tags, name='available_tags'),

    # Frontend views
    path('', views.dashboard_view, name='dashboard'),
    path('ec2-instances/', views.ec2_instances_view, name='ec2_instances'),
    path('ssm-status/', views.ssm_status_view, name='ssm_status'),
    path('accounts/', views.accounts_view, name='accounts'),
]
